<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:21 PDT 2023 -->
<title>VMRouter</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="VMRouter";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/UUIDGenerator.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/VMRouter.html" target="_top">Frames</a></li>
<li><a href="VMRouter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.server.userutil</div>
<h2 title="Class VMRouter" class="title">Class VMRouter</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.server.userutil.VMRouter</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">VMRouter</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Utility class used to dispatch messages to channels.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/VMRouter.html#VMRouter--">VMRouter</a></span>()</code>
<div class="block">Instantiates a VMRouter object.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/VMRouter.html#routeMessage-java.lang.String-com.mirth.connect.server.userutil.RawMessage-">routeMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelName,
            <a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a>&nbsp;rawMessage)</code>
<div class="block">Dispatches a message to a channel, specified by the deployed channel name.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/VMRouter.html#routeMessage-java.lang.String-java.lang.String-">routeMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelName,
            <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</code>
<div class="block">Dispatches a message to a channel, specified by the deployed channel name.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/VMRouter.html#routeMessageByChannelId-java.lang.String-com.mirth.connect.server.userutil.RawMessage-">routeMessageByChannelId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                       <a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a>&nbsp;rawMessage)</code>
<div class="block">Dispatches a message to a channel, specified by the deployed channel ID.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/VMRouter.html#routeMessageByChannelId-java.lang.String-java.lang.String-">routeMessageByChannelId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</code>
<div class="block">Dispatches a message to a channel, specified by the deployed channel ID.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="VMRouter--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>VMRouter</h4>
<pre>public&nbsp;VMRouter()</pre>
<div class="block">Instantiates a VMRouter object.</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="routeMessage-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>routeMessage</h4>
<pre>public&nbsp;<a href="../../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a>&nbsp;routeMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelName,
                             <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</pre>
<div class="block">Dispatches a message to a channel, specified by the deployed channel name. If the dispatch
 fails for any reason (for example, if the target channel is not started), a Response object
 with the ERROR status and the error message will be returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelName</code> - The name of the deployed channel to dispatch the message to.</dd>
<dd><code>message</code> - The message to dispatch to the channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Response object returned by the channel, if its source connector is configured to
         return one.</dd>
</dl>
</li>
</ul>
<a name="routeMessage-java.lang.String-com.mirth.connect.server.userutil.RawMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>routeMessage</h4>
<pre>public&nbsp;<a href="../../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a>&nbsp;routeMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelName,
                             <a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a>&nbsp;rawMessage)</pre>
<div class="block">Dispatches a message to a channel, specified by the deployed channel name. If the dispatch
 fails for any reason (for example, if the target channel is not started), a Response object
 with the ERROR status and the error message will be returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelName</code> - The name of the deployed channel to dispatch the message to.</dd>
<dd><code>rawMessage</code> - A RawMessage object to dispatch to the channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Response object returned by the channel, if its source connector is configured to
         return one.</dd>
</dl>
</li>
</ul>
<a name="routeMessageByChannelId-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>routeMessageByChannelId</h4>
<pre>public&nbsp;<a href="../../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a>&nbsp;routeMessageByChannelId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                                        <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</pre>
<div class="block">Dispatches a message to a channel, specified by the deployed channel ID. If the dispatch
 fails for any reason (for example, if the target channel is not started), a Response object
 with the ERROR status and the error message will be returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The ID of the deployed channel to dispatch the message to.</dd>
<dd><code>message</code> - The message to dispatch to the channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Response object returned by the channel, if its source connector is configured to
         return one.</dd>
</dl>
</li>
</ul>
<a name="routeMessageByChannelId-java.lang.String-com.mirth.connect.server.userutil.RawMessage-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>routeMessageByChannelId</h4>
<pre>public&nbsp;<a href="../../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a>&nbsp;routeMessageByChannelId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                                        <a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a>&nbsp;rawMessage)</pre>
<div class="block">Dispatches a message to a channel, specified by the deployed channel ID. If the dispatch
 fails for any reason (for example, if the target channel is not started), a Response object
 with the ERROR status and the error message will be returned.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The ID of the deployed channel to dispatch the message to.</dd>
<dd><code>rawMessage</code> - A RawMessage object to dispatch to the channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Response object returned by the channel, if its source connector is configured to
         return one.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/UUIDGenerator.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/VMRouter.html" target="_top">Frames</a></li>
<li><a href="VMRouter.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
