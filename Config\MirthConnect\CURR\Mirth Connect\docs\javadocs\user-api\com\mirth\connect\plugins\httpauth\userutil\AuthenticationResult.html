<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:17 PDT 2023 -->
<title>AuthenticationResult</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AuthenticationResult";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":9,"i2":9,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":9,"i12":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html" title="enum in com.mirth.connect.plugins.httpauth.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" target="_top">Frames</a></li>
<li><a href="AuthenticationResult.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.plugins.httpauth.userutil</div>
<h2 title="Class AuthenticationResult" class="title">Class AuthenticationResult</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.plugins.httpauth.userutil.AuthenticationResult</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">AuthenticationResult</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">This class represents the result of an HTTP authentication attempt, used to accept or reject
 requests coming into HTTP-based source connectors.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#AuthenticationResult-com.mirth.connect.plugins.httpauth.userutil.AuthStatus-">AuthenticationResult</a></span>(<a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html" title="enum in com.mirth.connect.plugins.httpauth.userutil">AuthStatus</a>&nbsp;status)</code>
<div class="block">Instantiates a new AuthenticationResult object.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#addResponseHeader-java.lang.String-java.lang.String-">addResponseHeader</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Adds a new response header to be sent along with the authentication response.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#Challenged-java.lang.String-">Challenged</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;authenticateHeader)</code>
<div class="block">Convenience method to create a new AuthenticationResult with the CHALLENGED status.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#Failure--">Failure</a></span>()</code>
<div class="block">Convenience method to create a new AuthenticationResult with the FAILURE status.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#getRealm--">getRealm</a></span>()</code>
<div class="block">Returns the realm that the request has been authenticated with.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#getResponseHeaders--">getResponseHeaders</a></span>()</code>
<div class="block">Returns the map of HTTP headers to be sent along with the authentication response.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html" title="enum in com.mirth.connect.plugins.httpauth.userutil">AuthStatus</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#getStatus--">getStatus</a></span>()</code>
<div class="block">Returns the accept/reject status of the authentication attempt.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#getUsername--">getUsername</a></span>()</code>
<div class="block">Returns the username that the request has been authenticated with.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#setRealm-java.lang.String-">setRealm</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;realm)</code>
<div class="block">Sets the realm that the request has been authenticated with.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#setResponseHeaders-java.util.Map-">setResponseHeaders</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&gt;&nbsp;responseHeaders)</code>
<div class="block">Sets the map of HTTP headers to be sent along with the authentication response.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#setStatus-com.mirth.connect.plugins.httpauth.userutil.AuthStatus-">setStatus</a></span>(<a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html" title="enum in com.mirth.connect.plugins.httpauth.userutil">AuthStatus</a>&nbsp;status)</code>
<div class="block">Sets the accept/reject status of the authentication attempt.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#setUsername-java.lang.String-">setUsername</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username)</code>
<div class="block">Sets the username that the request has been authenticated with.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#Success--">Success</a></span>()</code>
<div class="block">Convenience method to create a new AuthenticationResult with the SUCCESS status.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#Success-java.lang.String-java.lang.String-">Success</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;realm)</code>
<div class="block">Convenience method to create a new AuthenticationResult with the SUCCESS status.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AuthenticationResult-com.mirth.connect.plugins.httpauth.userutil.AuthStatus-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AuthenticationResult</h4>
<pre>public&nbsp;AuthenticationResult(<a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html" title="enum in com.mirth.connect.plugins.httpauth.userutil">AuthStatus</a>&nbsp;status)</pre>
<div class="block">Instantiates a new AuthenticationResult object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>status</code> - The accept/reject status to use.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatus</h4>
<pre>public&nbsp;<a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html" title="enum in com.mirth.connect.plugins.httpauth.userutil">AuthStatus</a>&nbsp;getStatus()</pre>
<div class="block">Returns the accept/reject status of the authentication attempt.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The accept/reject status of the authentication attempt.</dd>
</dl>
</li>
</ul>
<a name="setStatus-com.mirth.connect.plugins.httpauth.userutil.AuthStatus-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStatus</h4>
<pre>public&nbsp;void&nbsp;setStatus(<a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html" title="enum in com.mirth.connect.plugins.httpauth.userutil">AuthStatus</a>&nbsp;status)</pre>
<div class="block">Sets the accept/reject status of the authentication attempt.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>status</code> - The accept/reject status to use.</dd>
</dl>
</li>
</ul>
<a name="getUsername--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUsername</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getUsername()</pre>
<div class="block">Returns the username that the request has been authenticated with.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The username that the request has been authenticated with.</dd>
</dl>
</li>
</ul>
<a name="setUsername-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUsername</h4>
<pre>public&nbsp;void&nbsp;setUsername(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username)</pre>
<div class="block">Sets the username that the request has been authenticated with.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>username</code> - The username that the request has been authenticated with.</dd>
</dl>
</li>
</ul>
<a name="getRealm--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRealm</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRealm()</pre>
<div class="block">Returns the realm that the request has been authenticated with.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The realm that the request has been authenticated with.</dd>
</dl>
</li>
</ul>
<a name="setRealm-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRealm</h4>
<pre>public&nbsp;void&nbsp;setRealm(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;realm)</pre>
<div class="block">Sets the realm that the request has been authenticated with.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>realm</code> - The realm that the request has been authenticated with.</dd>
</dl>
</li>
</ul>
<a name="getResponseHeaders--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponseHeaders</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&gt;&nbsp;getResponseHeaders()</pre>
<div class="block">Returns the map of HTTP headers to be sent along with the authentication response.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The map of HTTP headers to be sent along with the authentication response.</dd>
</dl>
</li>
</ul>
<a name="setResponseHeaders-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResponseHeaders</h4>
<pre>public&nbsp;void&nbsp;setResponseHeaders(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&gt;&nbsp;responseHeaders)</pre>
<div class="block">Sets the map of HTTP headers to be sent along with the authentication response.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>responseHeaders</code> - The map of HTTP headers to be sent along with the authentication response.</dd>
</dl>
</li>
</ul>
<a name="addResponseHeader-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addResponseHeader</h4>
<pre>public&nbsp;void&nbsp;addResponseHeader(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;key,
                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Adds a new response header to be sent along with the authentication response.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>key</code> - The name of the header.</dd>
<dd><code>value</code> - The value of the header.</dd>
</dl>
</li>
</ul>
<a name="Challenged-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Challenged</h4>
<pre>public static&nbsp;<a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a>&nbsp;Challenged(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;authenticateHeader)</pre>
<div class="block">Convenience method to create a new AuthenticationResult with the CHALLENGED status.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>authenticateHeader</code> - The value to include in the WWW-Authenticate response header.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The created AuthenticationResult object.</dd>
</dl>
</li>
</ul>
<a name="Success--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Success</h4>
<pre>public static&nbsp;<a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a>&nbsp;Success()</pre>
<div class="block">Convenience method to create a new AuthenticationResult with the SUCCESS status.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The created AuthenticationResult object.</dd>
</dl>
</li>
</ul>
<a name="Success-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Success</h4>
<pre>public static&nbsp;<a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a>&nbsp;Success(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
                                           <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;realm)</pre>
<div class="block">Convenience method to create a new AuthenticationResult with the SUCCESS status.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>username</code> - The username that the request has been authenticated with.</dd>
<dd><code>realm</code> - The realm that the request has been authenticated with.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The created AuthenticationResult object.</dd>
</dl>
</li>
</ul>
<a name="Failure--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Failure</h4>
<pre>public static&nbsp;<a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a>&nbsp;Failure()</pre>
<div class="block">Convenience method to create a new AuthenticationResult with the FAILURE status.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The created AuthenticationResult object.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../../../../com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html" title="enum in com.mirth.connect.plugins.httpauth.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../../index.html?com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" target="_top">Frames</a></li>
<li><a href="AuthenticationResult.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
