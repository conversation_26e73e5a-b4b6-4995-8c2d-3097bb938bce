<connectorMetaData path="js">
	<name>JavaScript Reader</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to execute arbitrary JavaScript to pull in messages to a channel.</description>
	<clientClassName>com.mirth.connect.connectors.js.JavaScriptReader</clientClassName>
	<serverClassName>com.mirth.connect.connectors.js.JavaScriptReceiver</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.js.JavaScriptReceiverProperties</sharedClassName>
	<library type="CLIENT" path="js-client.jar" />
	<library type="SHARED" path="js-shared.jar" />
	<library type="SERVER" path="js-server.jar" />
	<transformers>ResultMapToXML</transformers>
	<protocol>js</protocol>
	<type>SOURCE</type>
</connectorMetaData>
