# PowerShell script to create MirthConnect service with parent folder name
# Author: Mobile Aspects
# Date: 2025-07-05

# Get the parent folder name (e.g., "CURR")
$parentFolder = Split-Path (Split-Path $PSScriptRoot -Parent) -Leaf

# Get the full path to mcservice.exe
$mcservicePath = Join-Path (Split-Path $PSScriptRoot -Parent) "mcservice.exe"

# Create service name with parent folder
$serviceName = "MA_MirthConnect_$parentFolder"
$displayName = "MA MirthConnect ($parentFolder)"

Write-Host "Creating MirthConnect service..." -ForegroundColor Green
Write-Host "Service Name: $serviceName" -ForegroundColor Yellow
Write-Host "Display Name: $displayName" -ForegroundColor Yellow
Write-Host "Executable Path: $mcservicePath" -ForegroundColor Yellow

$params = @{
  Name = $serviceName
  BinaryPathName = "`"$mcservicePath`""
  DisplayName = $displayName
  StartupType = "Automatic"
}

try {
    New-Service @params
    Write-Host "Service '$serviceName' created successfully!" -ForegroundColor Green

    # Optionally start the service
    $startService = Read-Host "Do you want to start the service now? (Y/N)"
    if ($startService -eq "Y" -or $startService -eq "y") {
        Start-Service -Name $serviceName
        Write-Host "Service '$serviceName' started successfully!" -ForegroundColor Green
    }
}
catch {
    Write-Host "Error creating service: $($_.Exception.Message)" -ForegroundColor Red
}