-server
-Xmx256m
-Djava.awt.headless=true
-Dapple.awt.UIElement=true
-Duser.dir=C:\Mobile Aspects\Config\MirthConnect\CURR\Mirth Connect
--add-modules=java.sql.rowset
--add-exports=java.base/com.sun.crypto.provider=ALL-UNNAMED
--add-exports=java.base/sun.security.provider=ALL-UNNAMED
--add-opens=java.base/java.lang=ALL-UNNAMED
--add-opens=java.base/java.lang.reflect=ALL-UNNAMED
--add-opens=java.base/java.math=ALL-UNNAMED
--add-opens=java.base/java.net=ALL-UNNAMED
--add-opens=java.base/java.security=ALL-UNNAMED
--add-opens=java.base/java.security.cert=ALL-UNNAMED
--add-opens=java.base/java.text=ALL-UNNAMED
--add-opens=java.base/java.util=ALL-UNNAMED
--add-opens=java.base/sun.security.pkcs=ALL-UNNAMED
--add-opens=java.base/sun.security.rsa=ALL-UNNAMED
--add-opens=java.base/sun.security.x509=ALL-UNNAMED
--add-opens=java.desktop/java.awt=ALL-UNNAMED
--add-opens=java.desktop/java.awt.color=ALL-UNNAMED
--add-opens=java.desktop/java.awt.font=ALL-UNNAMED
--add-opens=java.xml/com.sun.org.apache.xalan.internal.xsltc.trax=ALL-UNNAMED