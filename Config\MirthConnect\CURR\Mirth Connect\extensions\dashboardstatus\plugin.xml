<pluginMetaData path="dashboardstatus">
	<name>Dashboard Connector Status Monitor</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This plugin provides a real-time connection status column on the Mirth Connect client</description>
	<serverClasses>
		<string>com.mirth.connect.plugins.dashboardstatus.DashboardConnectorStatusMonitor</string>
	</serverClasses>
	<clientClasses>
		<string>com.mirth.connect.plugins.dashboardstatus.DashboardConnectorStatusColumn</string>
		<string weight="110">com.mirth.connect.plugins.dashboardstatus.DashboardConnectorStatusClient</string>
	</clientClasses>
	<library type="SHARED" path="dashboardstatus-shared.jar" />
	<library type="SERVER" path="dashboardstatus-server.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.plugins.dashboardstatus.DashboardConnectorStatusServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.plugins.dashboardstatus.DashboardConnectorStatusServlet"/>
</pluginMetaData>
