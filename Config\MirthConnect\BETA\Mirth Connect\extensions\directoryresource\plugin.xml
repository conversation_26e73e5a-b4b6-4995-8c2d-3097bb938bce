<pluginMetaData path="directoryresource">
	<name>Directory Resource Plugin</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This plugin allows a directory to be used as a source for libraries to include in channels.</description>
	<serverClasses>
		<string>com.mirth.connect.plugins.directoryresource.DirectoryResourcePlugin</string>
	</serverClasses>
	<clientClasses>
		<string weight="110">com.mirth.connect.plugins.directoryresource.DirectoryResourceClientPlugin</string>
	</clientClasses>
	<library type="SERVER" path="directoryresource-server.jar" />
	<library type="CLIENT" path="directoryresource-client.jar" />
	<library type="SHARED" path="directoryresource-shared.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.plugins.directoryresource.DirectoryResourceServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.plugins.directoryresource.DirectoryResourceServlet"/>
</pluginMetaData>
