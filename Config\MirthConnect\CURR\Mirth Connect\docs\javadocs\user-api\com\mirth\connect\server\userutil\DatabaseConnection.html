<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:18 PDT 2023 -->
<title>DatabaseConnection</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DatabaseConnection";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/ContextFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnectionFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/DatabaseConnection.html" target="_top">Frames</a></li>
<li><a href="DatabaseConnection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.server.userutil</div>
<h2 title="Class DatabaseConnection" class="title">Class DatabaseConnection</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.server.userutil.DatabaseConnection</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DatabaseConnection</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Provides the ability to run SQL queries again the database connection object instantiated using
 DatabaseConnectionFactory.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#DatabaseConnection-java.sql.Driver-java.lang.String-">DatabaseConnection</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/sql/Driver.html?is-external=true" title="class or interface in java.sql">Driver</a>&nbsp;driver,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;address)</code>
<div class="block">Instantiates a new database connection with the given driver instance and server address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#DatabaseConnection-java.sql.Driver-java.lang.String-java.util.Properties-">DatabaseConnection</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/sql/Driver.html?is-external=true" title="class or interface in java.sql">Driver</a>&nbsp;driver,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;address,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Properties.html?is-external=true" title="class or interface in java.util">Properties</a>&nbsp;info)</code>
<div class="block">Instantiates a new database connection with the given driver instance, server address, and
 connection arguments.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#DatabaseConnection-java.lang.String-">DatabaseConnection</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;address)</code>
<div class="block">Instantiates a new database connection with the given server address.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#DatabaseConnection-java.lang.String-java.util.Properties-">DatabaseConnection</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;address,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Properties.html?is-external=true" title="class or interface in java.util">Properties</a>&nbsp;info)</code>
<div class="block">Instantiates a new database connection with the given server address and connection
 arguments.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#close--">close</a></span>()</code>
<div class="block">Closes the database connection.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#commit--">commit</a></span>()</code>
<div class="block">Makes all changes made since the previous commit/rollback permanent and releases any database
 locks currently held by this DatabaseConnection object.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/javax/sql/rowset/CachedRowSet.html?is-external=true" title="class or interface in javax.sql.rowset">CachedRowSet</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#executeCachedQuery-java.lang.String-">executeCachedQuery</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression)</code>
<div class="block">Executes a query on the database and returns a CachedRowSet.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/javax/sql/rowset/CachedRowSet.html?is-external=true" title="class or interface in javax.sql.rowset">CachedRowSet</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#executeCachedQuery-java.lang.String-java.util.List-">executeCachedQuery</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;parameters)</code>
<div class="block">Executes a prepared query on the database and returns a CachedRowSet.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#executeUpdate-java.lang.String-">executeUpdate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression)</code>
<div class="block">Executes an INSERT/UPDATE on the database and returns the row count.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#executeUpdate-java.lang.String-java.util.List-">executeUpdate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;parameters)</code>
<div class="block">Executes a prepared INSERT/UPDATE statement on the database and returns the row count.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/javax/sql/rowset/CachedRowSet.html?is-external=true" title="class or interface in javax.sql.rowset">CachedRowSet</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#executeUpdateAndGetGeneratedKeys-java.lang.String-">executeUpdateAndGetGeneratedKeys</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression)</code>
<div class="block">Executes an INSERT/UPDATE statement on the database and returns a CachedRowSet containing any
 generated keys.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/javax/sql/rowset/CachedRowSet.html?is-external=true" title="class or interface in javax.sql.rowset">CachedRowSet</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#executeUpdateAndGetGeneratedKeys-java.lang.String-java.util.List-">executeUpdateAndGetGeneratedKeys</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression,
                                <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;parameters)</code>
<div class="block">Executes a prepared INSERT/UPDATE statement on the database and returns a CachedRowSet
 containing any generated keys.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#getAddress--">getAddress</a></span>()</code>
<div class="block">Returns the server address.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/Connection.html?is-external=true" title="class or interface in java.sql">Connection</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#getConnection--">getConnection</a></span>()</code>
<div class="block">Returns the database connection (java.sql.Connection) this class is using.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#rollback--">rollback</a></span>()</code>
<div class="block">Undoes all changes made in the current transaction and releases any database locks currently
 held by this Connection object.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html#setAutoCommit-boolean-">setAutoCommit</a></span>(boolean&nbsp;autoCommit)</code>
<div class="block">Sets this connection's auto-commit mode to the given state.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DatabaseConnection-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DatabaseConnection</h4>
<pre>public&nbsp;DatabaseConnection(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;address)
                   throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Instantiates a new database connection with the given server address.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>address</code> - The server address to connect to.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
<a name="DatabaseConnection-java.lang.String-java.util.Properties-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DatabaseConnection</h4>
<pre>public&nbsp;DatabaseConnection(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;address,
                          <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Properties.html?is-external=true" title="class or interface in java.util">Properties</a>&nbsp;info)
                   throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Instantiates a new database connection with the given server address and connection
 arguments.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>address</code> - The server address to connect to.</dd>
<dd><code>info</code> - A Properties object containing all applicable connection arguments.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
<a name="DatabaseConnection-java.sql.Driver-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>DatabaseConnection</h4>
<pre>public&nbsp;DatabaseConnection(<a href="https://docs.oracle.com/javase/8/docs/api/java/sql/Driver.html?is-external=true" title="class or interface in java.sql">Driver</a>&nbsp;driver,
                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;address)
                   throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Instantiates a new database connection with the given driver instance and server address.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>driver</code> - The explicit driver instance to connect with.</dd>
<dd><code>address</code> - The server address to connect to.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
<a name="DatabaseConnection-java.sql.Driver-java.lang.String-java.util.Properties-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DatabaseConnection</h4>
<pre>public&nbsp;DatabaseConnection(<a href="https://docs.oracle.com/javase/8/docs/api/java/sql/Driver.html?is-external=true" title="class or interface in java.sql">Driver</a>&nbsp;driver,
                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;address,
                          <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Properties.html?is-external=true" title="class or interface in java.util">Properties</a>&nbsp;info)
                   throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Instantiates a new database connection with the given driver instance, server address, and
 connection arguments.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>driver</code> - The explicit driver instance to connect with.</dd>
<dd><code>address</code> - The server address to connect to.</dd>
<dd><code>info</code> - A Properties object containing all applicable connection arguments.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getAddress--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAddress</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getAddress()</pre>
<div class="block">Returns the server address.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The server address.</dd>
</dl>
</li>
</ul>
<a name="executeCachedQuery-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executeCachedQuery</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/javax/sql/rowset/CachedRowSet.html?is-external=true" title="class or interface in javax.sql.rowset">CachedRowSet</a>&nbsp;executeCachedQuery(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression)
                                throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Executes a query on the database and returns a CachedRowSet.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>expression</code> - The query expression to be executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The result of the query, as a CachedRowSet.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
<a name="executeUpdate-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executeUpdate</h4>
<pre>public&nbsp;int&nbsp;executeUpdate(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression)
                  throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Executes an INSERT/UPDATE on the database and returns the row count.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>expression</code> - The statement to be executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A count of the number of updated rows.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
<a name="executeUpdate-java.lang.String-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executeUpdate</h4>
<pre>public&nbsp;int&nbsp;executeUpdate(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression,
                         <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;parameters)
                  throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Executes a prepared INSERT/UPDATE statement on the database and returns the row count.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>expression</code> - The prepared statement to be executed.</dd>
<dd><code>parameters</code> - The parameters for the prepared statement.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A count of the number of updated rows.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
<a name="executeCachedQuery-java.lang.String-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executeCachedQuery</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/javax/sql/rowset/CachedRowSet.html?is-external=true" title="class or interface in javax.sql.rowset">CachedRowSet</a>&nbsp;executeCachedQuery(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression,
                                       <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;parameters)
                                throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Executes a prepared query on the database and returns a CachedRowSet.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>expression</code> - The prepared statement to be executed.</dd>
<dd><code>parameters</code> - The parameters for the prepared statement.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The result of the query, as a CachedRowSet.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
<a name="close--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>close</h4>
<pre>public&nbsp;void&nbsp;close()</pre>
<div class="block">Closes the database connection.</div>
</li>
</ul>
<a name="setAutoCommit-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoCommit</h4>
<pre>public&nbsp;void&nbsp;setAutoCommit(boolean&nbsp;autoCommit)
                   throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Sets this connection's auto-commit mode to the given state.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>autoCommit</code> - The value (true or false) to set the connection's auto-commit mode to.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
<a name="rollback--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rollback</h4>
<pre>public&nbsp;void&nbsp;rollback()
              throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Undoes all changes made in the current transaction and releases any database locks currently
 held by this Connection object.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
<a name="commit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>commit</h4>
<pre>public&nbsp;void&nbsp;commit()
            throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Makes all changes made since the previous commit/rollback permanent and releases any database
 locks currently held by this DatabaseConnection object.</div>
<dl>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
<a name="executeUpdateAndGetGeneratedKeys-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executeUpdateAndGetGeneratedKeys</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/javax/sql/rowset/CachedRowSet.html?is-external=true" title="class or interface in javax.sql.rowset">CachedRowSet</a>&nbsp;executeUpdateAndGetGeneratedKeys(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression)
                                              throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Executes an INSERT/UPDATE statement on the database and returns a CachedRowSet containing any
 generated keys.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>expression</code> - The statement to be executed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A CachedRowSet containing any generated keys.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
<a name="executeUpdateAndGetGeneratedKeys-java.lang.String-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>executeUpdateAndGetGeneratedKeys</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/javax/sql/rowset/CachedRowSet.html?is-external=true" title="class or interface in javax.sql.rowset">CachedRowSet</a>&nbsp;executeUpdateAndGetGeneratedKeys(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;expression,
                                                     <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;parameters)
                                              throws <a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></pre>
<div class="block">Executes a prepared INSERT/UPDATE statement on the database and returns a CachedRowSet
 containing any generated keys.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>expression</code> - The prepared statement to be executed.</dd>
<dd><code>parameters</code> - The parameters for the prepared statement.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A CachedRowSet containing any generated keys.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/sql/SQLException.html?is-external=true" title="class or interface in java.sql">SQLException</a></code> - If a database access error occurs.</dd>
</dl>
</li>
</ul>
<a name="getConnection--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getConnection</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/sql/Connection.html?is-external=true" title="class or interface in java.sql">Connection</a>&nbsp;getConnection()</pre>
<div class="block">Returns the database connection (java.sql.Connection) this class is using.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The underlying java.sql.Connection object.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/ContextFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnectionFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/DatabaseConnection.html" target="_top">Frames</a></li>
<li><a href="DatabaseConnection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
