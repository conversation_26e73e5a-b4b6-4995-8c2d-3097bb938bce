<connectorMetaData path="ws">
	<name>Web Service Sender</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to invoke remote Web Services over HTTP.</description>
	<clientClassName>com.mirth.connect.connectors.ws.WebServiceSender</clientClassName>
	<serverClassName>com.mirth.connect.connectors.ws.WebServiceDispatcher</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.ws.WebServiceDispatcherProperties</sharedClassName>
	<transformers></transformers>
	<protocol>ws</protocol>
	<type>DESTINATION</type>
	<library type="SERVER" path="ws-server.jar" />
	<library type="CLIENT" path="ws-client.jar" />
	<library type="SHARED" path="ws-shared.jar" />
	<library type="SERVER" path="lib/soapui-4.0.1.jar" />
	<library type="SERVER" path="lib/wsdl4j-1.6.2-fixed.jar" />
	<library type="SERVER" path="lib/xbean-fixed-2.4.0.jar" />
	<library type="SERVER" path="lib/soapui-xmlbeans-4.0.1.jar" />
	<library type="SERVER" path="lib/policy-xmlbeans-1.5.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.connectors.ws.WebServiceConnectorServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.connectors.ws.WebServiceConnectorServlet"/>
</connectorMetaData>
