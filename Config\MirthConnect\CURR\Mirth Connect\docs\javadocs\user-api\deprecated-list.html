<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:22 PDT 2023 -->
<title>Deprecated List</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Deprecated List";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Deprecated</li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">Frames</a></li>
<li><a href="deprecated-list.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Deprecated API" class="title">Deprecated API</h1>
<h2 title="Contents">Contents</h2>
<ul>
<li><a href="#method">Deprecated Methods</a></li>
<li><a href="#constructor">Deprecated Constructors</a></li>
</ul>
</div>
<div class="contentContainer"><a name="method">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="Deprecated Methods table, listing deprecated methods, and an explanation">
<caption><span>Deprecated Methods</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/ACKGenerator.html#generateAckResponse-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">com.mirth.connect.server.userutil.ACKGenerator.generateAckResponse(String, String, String, String, String, String)</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             generateAckResponse(message, isXML, acknowledgementCode, textMessage, dateFormat,
             errorMessage) instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/mirth/connect/userutil/MessageParameters.html#get-java.lang.String-">com.mirth.connect.userutil.MessageParameters.get(String)</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use getParameter(key)
             or getParameterList(key) instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/mirth/connect/userutil/MessageHeaders.html#get-java.lang.String-">com.mirth.connect.userutil.MessageHeaders.get(String)</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use getHeader(key) or
             getHeaderList(key) instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/RawMessage.html#getChannelMap--">com.mirth.connect.server.userutil.RawMessage.getChannelMap()</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="com/mirth/connect/server/userutil/RawMessage.html#getSourceMap--"><code>RawMessage.getSourceMap()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getContent-com.mirth.connect.userutil.ContentType-">com.mirth.connect.userutil.ImmutableConnectorMessage.getContent(ContentType)</a>
<div class="block"><span class="deprecationComment">The getContent(contentType) method has been deprecated and will soon be removed.
             Please use getMessageContent(contentType) instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getDestinationNameMap--">com.mirth.connect.userutil.ImmutableConnectorMessage.getDestinationNameMap()</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getDestinationIdMap--"><code>getDestinationIdMap()</code></a> instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getDestinationNameMap--">com.mirth.connect.userutil.ImmutableMessage.getDestinationNameMap()</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="com/mirth/connect/userutil/ImmutableMessage.html#getDestinationIdMap--"><code>getDestinationIdMap()</code></a> instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getEDISerializer-java.lang.String-java.lang.String-java.lang.String-">com.mirth.connect.server.userutil.SerializerFactory.getEDISerializer(String, String, String)</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer--">com.mirth.connect.server.userutil.SerializerFactory.getHL7Serializer()</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer-boolean-boolean-">com.mirth.connect.server.userutil.SerializerFactory.getHL7Serializer(boolean, boolean)</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer-boolean-boolean-boolean-">com.mirth.connect.server.userutil.SerializerFactory.getHL7Serializer(boolean, boolean, boolean)</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer-boolean-boolean-boolean-boolean-">com.mirth.connect.server.userutil.SerializerFactory.getHL7Serializer(boolean, boolean, boolean, boolean)</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer-boolean-boolean-boolean-boolean-boolean-">com.mirth.connect.server.userutil.SerializerFactory.getHL7Serializer(boolean, boolean, boolean, boolean, boolean)</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getNCPDPSerializer-java.lang.String-java.lang.String-java.lang.String-">com.mirth.connect.server.userutil.SerializerFactory.getNCPDPSerializer(String, String, String)</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getNCPDPSerializer-java.lang.String-java.lang.String-java.lang.String-boolean-">com.mirth.connect.server.userutil.SerializerFactory.getNCPDPSerializer(String, String, String, boolean)</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getReceivedDate--">com.mirth.connect.userutil.ImmutableMessage.getReceivedDate()</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. This method currently returns
             the received date of the source connector message.</span></div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getX12Serializer-boolean-">com.mirth.connect.server.userutil.SerializerFactory.getX12Serializer(boolean)</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/RawMessage.html#setChannelMap-java.util.Map-">com.mirth.connect.server.userutil.RawMessage.setChannelMap(Map&lt;String, Object&gt;)</a>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="com/mirth/connect/server/userutil/RawMessage.html#setSourceMap-java.util.Map-"><code>setSourceMap(sourceMap)</code></a> instead.</span></div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="constructor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<table class="deprecatedSummary" border="0" cellpadding="3" cellspacing="0" summary="Deprecated Constructors table, listing deprecated constructors, and an explanation">
<caption><span>Deprecated Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="com/mirth/connect/server/userutil/ACKGenerator.html#ACKGenerator--">com.mirth.connect.server.userutil.ACKGenerator()</a></td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="navBarCell1Rev">Deprecated</li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?deprecated-list.html" target="_top">Frames</a></li>
<li><a href="deprecated-list.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
