# PowerShell script to create MirthConnect service with parent folder name
# Author: Mobile Aspects
# Date: 2025-07-05

# Get the parent folder name (e.g., "CURR") - go up 3 levels from Scripts/Mirth_CreateService
$parentFolder = Split-Path (Split-Path (Split-Path $PSScriptRoot -Parent) -Parent) -Leaf

# Get the full path to mcservice.exe (in the CURR folder, not Scripts folder)
$mcservicePath = Join-Path (Split-Path (Split-Path $PSScriptRoot -Parent) -Parent) "mcservice.exe"

# Create service name with parent folder
$serviceName = "MA_MirthConnect_$parentFolder"
$displayName = "MA MirthConnect ($parentFolder)"

Write-Host "Creating MirthConnect service..." -ForegroundColor Green
Write-Host "Service Name: $serviceName" -ForegroundColor Yellow
Write-Host "Display Name: $displayName" -ForegroundColor Yellow
Write-Host "Executable Path: $mcservicePath" -ForegroundColor Yellow

$params = @{
  Name = $serviceName
  BinaryPathName = "`"$mcservicePath`""
  DisplayName = $displayName
  StartupType = "Automatic"
}

try {
    # Create the service with Automatic startup
    New-Service @params
    Write-Host "Service '$serviceName' created successfully!" -ForegroundColor Green

    # Set to Automatic (Delayed Start) using sc command
    $scResult = & sc.exe config $serviceName start= delayed-auto
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Service startup type set to Automatic (Delayed Start)!" -ForegroundColor Green
    } else {
        Write-Host "Warning: Could not set delayed start. Service will use regular Automatic startup." -ForegroundColor Yellow
    }

    # Optionally start the service
    $startService = Read-Host "Do you want to start the service now? (Y/N)"
    if ($startService -eq "Y" -or $startService -eq "y") {
        Start-Service -Name $serviceName
        Write-Host "Service '$serviceName' started successfully!" -ForegroundColor Green
    }
}
catch {
    Write-Host "Error creating service: $($_.Exception.Message)" -ForegroundColor Red
}
