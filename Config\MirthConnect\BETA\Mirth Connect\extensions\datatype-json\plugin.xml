<pluginMetaData path="datatype-json">
	<name>JSON Data Type</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This plugin provides support for the JSON data type</description>
	<serverClasses>
		<string>com.mirth.connect.plugins.datatypes.json.JSONDataTypeServerPlugin</string>
	</serverClasses>
	<clientClasses>
		<string>com.mirth.connect.plugins.datatypes.json.JSONDataTypeClientPlugin</string>
	</clientClasses>
	<library type="CLIENT" path="datatype-json-client.jar" />
	<library type="SHARED" path="datatype-json-shared.jar" />
	<library type="SERVER" path="datatype-json-server.jar" />
</pluginMetaData>
