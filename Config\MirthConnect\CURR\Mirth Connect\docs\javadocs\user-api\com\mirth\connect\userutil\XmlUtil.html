<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:21 PDT 2023 -->
<title>XmlUtil</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="XmlUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/mirth/connect/userutil/XmlUtil.html" target="_top">Frames</a></li>
<li><a href="XmlUtil.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.userutil</div>
<h2 title="Class XmlUtil" class="title">Class XmlUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.userutil.XmlUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">XmlUtil</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Provides XML utility methods.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/XmlUtil.html#decode-java.lang.String-">decode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;entity)</code>
<div class="block">Converts an XML/HTML entity reference into a string with the literal character.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/XmlUtil.html#encode-char-">encode</a></span>(char&nbsp;s)</code>
<div class="block">Encodes a character into the corresponding XML/HTML entity.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/XmlUtil.html#encode-char:A-int-int-">encode</a></span>(char[]&nbsp;text,
      int&nbsp;start,
      int&nbsp;length)</code>
<div class="block">Converts a character array, encoding characters into the corresponding XML/HTML entities as
 needed.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/XmlUtil.html#encode-java.lang.String-">encode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;s)</code>
<div class="block">Converts a string, encoding characters into the corresponding XML/HTML entities as needed.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/XmlUtil.html#prettyPrint-java.lang.String-">prettyPrint</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;input)</code>
<div class="block">Formats an XML string with indented markup.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/XmlUtil.html#toJson-java.lang.String-">toJson</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;xmlString)</code>
<div class="block">Converts an XML string to JSON, while stripping bound namespace prefixes.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/XmlUtil.html#toJson-java.lang.String-boolean-">toJson</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;xmlString,
      boolean&nbsp;normalizeNamespaces)</code>
<div class="block">Converts an XML string to JSON.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/XmlUtil.html#toJson-java.lang.String-boolean-boolean-boolean-boolean-">toJson</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;xmlString,
      boolean&nbsp;autoArray,
      boolean&nbsp;autoPrimitive,
      boolean&nbsp;prettyPrint,
      boolean&nbsp;normalizeNamespaces)</code>
<div class="block">Converts an XML string to JSON.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/XmlUtil.html#toJson-java.lang.String-boolean-boolean-boolean-boolean-boolean-boolean-">toJson</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;xmlString,
      boolean&nbsp;autoArray,
      boolean&nbsp;autoPrimitive,
      boolean&nbsp;prettyPrint,
      boolean&nbsp;normalizeNamespaces,
      boolean&nbsp;alwaysArray,
      boolean&nbsp;alwaysExpandObjects)</code>
<div class="block">Converts an XML string to JSON.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="prettyPrint-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>prettyPrint</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;prettyPrint(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;input)</pre>
<div class="block">Formats an XML string with indented markup.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>input</code> - The XML string to format.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The formatted XML string.</dd>
</dl>
</li>
</ul>
<a name="decode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decode</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;decode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;entity)</pre>
<div class="block">Converts an XML/HTML entity reference into a string with the literal character.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>entity</code> - The XML/HTML entity to decode.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A string containing the decoded character.</dd>
</dl>
</li>
</ul>
<a name="encode-char-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encode</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;encode(char&nbsp;s)</pre>
<div class="block">Encodes a character into the corresponding XML/HTML entity.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>s</code> - The character to encode.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The encoded XML/HTML entity.</dd>
</dl>
</li>
</ul>
<a name="encode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encode</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;encode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;s)</pre>
<div class="block">Converts a string, encoding characters into the corresponding XML/HTML entities as needed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>s</code> - The string to encode.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The encoded string with replaced XML/HTML entities.</dd>
</dl>
</li>
</ul>
<a name="encode-char:A-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encode</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;encode(char[]&nbsp;text,
                            int&nbsp;start,
                            int&nbsp;length)</pre>
<div class="block">Converts a character array, encoding characters into the corresponding XML/HTML entities as
 needed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>text</code> - The character array to encode.</dd>
<dd><code>start</code> - The index to start at in the character array.</dd>
<dd><code>length</code> - The maximum amount of characters to read from the array.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The encoded string with replaced XML/HTML entities.</dd>
</dl>
</li>
</ul>
<a name="toJson-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toJson</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toJson(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;xmlString)
                     throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Converts an XML string to JSON, while stripping bound namespace prefixes.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>xmlString</code> - The XML string to convert.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted JSON string.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If conversion failed.</dd>
</dl>
</li>
</ul>
<a name="toJson-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toJson</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toJson(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;xmlString,
                            boolean&nbsp;normalizeNamespaces)
                     throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Converts an XML string to JSON.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>xmlString</code> - The XML string to convert.</dd>
<dd><code>normalizeNamespaces</code> - Whether or not to normalize namespaces by stripping prefixes.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted JSON string.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If conversion failed.</dd>
</dl>
</li>
</ul>
<a name="toJson-java.lang.String-boolean-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toJson</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toJson(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;xmlString,
                            boolean&nbsp;autoArray,
                            boolean&nbsp;autoPrimitive,
                            boolean&nbsp;prettyPrint,
                            boolean&nbsp;normalizeNamespaces)
                     throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Converts an XML string to JSON.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>xmlString</code> - The XML string to convert.</dd>
<dd><code>autoArray</code> - If true, sibling nodes with the same tag name will be consolidated into a JSON
            array. If false, multiple properties with the same name will be present.</dd>
<dd><code>autoPrimitive</code> - If true, element text will be converted to JSON primitive values where applicable.
            If false, element text will always be converted to string values.</dd>
<dd><code>prettyPrint</code> - Whether or not to fully indent the JSON output.</dd>
<dd><code>normalizeNamespaces</code> - Whether or not to normalize namespaces by stripping prefixes.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted JSON string.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If conversion failed.</dd>
</dl>
</li>
</ul>
<a name="toJson-java.lang.String-boolean-boolean-boolean-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toJson</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toJson(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;xmlString,
                            boolean&nbsp;autoArray,
                            boolean&nbsp;autoPrimitive,
                            boolean&nbsp;prettyPrint,
                            boolean&nbsp;normalizeNamespaces,
                            boolean&nbsp;alwaysArray,
                            boolean&nbsp;alwaysExpandObjects)
                     throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Converts an XML string to JSON.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>xmlString</code> - The XML string to convert.</dd>
<dd><code>autoArray</code> - If true, sibling nodes with the same tag name will be consolidated into a JSON
            array. If false, multiple properties with the same name will be present.</dd>
<dd><code>autoPrimitive</code> - If true, element text will be converted to JSON primitive values where applicable.
            If false, element text will always be converted to string values.</dd>
<dd><code>prettyPrint</code> - Whether or not to fully indent the JSON output.</dd>
<dd><code>normalizeNamespaces</code> - Whether or not to normalize namespaces by stripping prefixes.</dd>
<dd><code>alwaysArray</code> - If true, all nodes except for the top-level object will be written into JSON
            arrays. Overrides the autoArray option.</dd>
<dd><code>alwaysExpandObjects</code> - If true, all values will be written in the expanded "$" syntax.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted JSON string.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If conversion failed.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/mirth/connect/userutil/XmlUtil.html" target="_top">Frames</a></li>
<li><a href="XmlUtil.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
