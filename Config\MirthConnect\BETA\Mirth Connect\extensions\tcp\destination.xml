<connectorMetaData path="tcp">
	<name>TCP Sender</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to send messages to a TCP server.</description>
	<clientClassName>com.mirth.connect.connectors.tcp.TcpSender</clientClassName>
	<serverClassName>com.mirth.connect.connectors.tcp.TcpDispatcher</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.tcp.TcpDispatcherProperties</sharedClassName>
	<library type="CLIENT" path="tcp-client.jar" />
	<library type="SHARED" path="tcp-shared.jar" />
	<library type="SERVER" path="tcp-server.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.connectors.tcp.TcpConnectorServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.connectors.tcp.TcpConnectorServlet"/>
	<transformers/>
	<protocol>tcp</protocol>
	<type>DESTINATION</type>
</connectorMetaData>
