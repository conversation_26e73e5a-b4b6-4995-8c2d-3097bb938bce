# Util_CopyConfigFiles.ps1 - PowerShell script to copy all configuration files to Config folder while preserving directory structure
# Author: Augment Agent
# Date: 2025-07-05
# Updated: 2025-07-05 - Renamed from copy-config-files.ps1, uses relative paths

param(
    [string]$SourceRoot = "../..",
    [string]$ConfigRoot = "../../Config"
)

# Define configuration file patterns
$ConfigPatterns = @(
    "*.orig",
    "*.properties",
    "*.config", 
    "*settings.json",
    "*.xml",
    "*.yml", 
    "*.yaml",
    "*.ini",
    "*.conf",
    "*.cfg",
    "*.env",
    "*.toml",
    "appsettings*.json",
    "web.config",
    "app.config",
    "libman.json",
    "*.deps.json",
    "*.runtimeconfig.json"
)

# Exclude patterns for files we don't want to copy (temp files, library files, etc.)
$ExcludePatterns = @(
    "*\temp\*",
    "*\appdata\temp\*",
    "*\dir.appdata\temp\*",
    "*\wwwroot\assets\libs\*",
    "*\wwwroot\assets\lang\*",
    "*\wwwroot\assets\Icons\*",
    "*\legal\*",
    "*\.install4j\*",
    "*\javachecker\*",
    "*\mcadministrator\*",
    "*\openjfx\*"
)

Write-Host "Starting configuration file copy process..." -ForegroundColor Green
Write-Host "Source Root: $SourceRoot" -ForegroundColor Yellow
Write-Host "Config Root: $ConfigRoot" -ForegroundColor Yellow

# Create Config directory if it doesn't exist
if (!(Test-Path $ConfigRoot)) {
    New-Item -ItemType Directory -Path $ConfigRoot -Force | Out-Null
    Write-Host "Created Config directory: $ConfigRoot" -ForegroundColor Green
}

# Get all configuration files
$AllConfigFiles = @()
foreach ($pattern in $ConfigPatterns) {
    $files = Get-ChildItem -Path $SourceRoot -Recurse -Include $pattern -File -ErrorAction SilentlyContinue
    $AllConfigFiles += $files
}

Write-Host "Found $($AllConfigFiles.Count) potential configuration files" -ForegroundColor Yellow

# Filter out excluded files
$FilteredConfigFiles = @()
foreach ($file in $AllConfigFiles) {
    $shouldExclude = $false
    foreach ($excludePattern in $ExcludePatterns) {
        if ($file.FullName -like $excludePattern) {
            $shouldExclude = $true
            break
        }
    }
    
    # Also exclude files already in Config directory (using relative path)
    $configPath = Resolve-Path $ConfigRoot -ErrorAction SilentlyContinue
    if ($configPath -and $file.FullName.StartsWith($configPath.Path, [System.StringComparison]::OrdinalIgnoreCase)) {
        $shouldExclude = $true
    }
    
    if (!$shouldExclude) {
        $FilteredConfigFiles += $file
    }
}

Write-Host "After filtering: $($FilteredConfigFiles.Count) configuration files to copy" -ForegroundColor Yellow

# Copy files while preserving directory structure
$CopiedCount = 0
$SkippedCount = 0
$OverwriteAll = $false
$SkipAll = $false

foreach ($file in $FilteredConfigFiles) {
    try {
        # Get relative path from source root
        $sourceRootPath = Resolve-Path $SourceRoot
        $relativePath = $file.FullName.Substring($sourceRootPath.Path.Length + 1)
        
        # Create destination path in Config folder
        $destPath = Join-Path $ConfigRoot $relativePath
        $destDir = Split-Path $destPath -Parent
        
        # Create destination directory if it doesn't exist
        if (!(Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }

        # Check if file already exists and handle based on user preference
        if (Test-Path $destPath) {
            if ($SkipAll) {
                Write-Host "Skipped (Skip All): $relativePath" -ForegroundColor Gray
                $SkippedCount++
            } elseif ($OverwriteAll) {
                Copy-Item -Path $file.FullName -Destination $destPath -Force
                Write-Host "Overwritten (Overwrite All): $relativePath" -ForegroundColor Green
                $CopiedCount++
            } else {
                Write-Host "File already exists: $relativePath" -ForegroundColor Yellow
                $choice = Read-Host "Do you want to overwrite it? (Y/N/A for Yes to All/S to Skip All)"

                switch ($choice.ToUpper()) {
                    "A" {
                        $OverwriteAll = $true
                        Copy-Item -Path $file.FullName -Destination $destPath -Force
                        Write-Host "Overwritten: $relativePath" -ForegroundColor Green
                        $CopiedCount++
                    }
                    "S" {
                        $SkipAll = $true
                        Write-Host "Skipped: $relativePath" -ForegroundColor Gray
                        $SkippedCount++
                    }
                    "Y" {
                        Copy-Item -Path $file.FullName -Destination $destPath -Force
                        Write-Host "Overwritten: $relativePath" -ForegroundColor Green
                        $CopiedCount++
                    }
                    default {
                        Write-Host "Skipped: $relativePath" -ForegroundColor Gray
                        $SkippedCount++
                    }
                }
            }
        } else {
            # Copy the file if it doesn't exist
            Copy-Item -Path $file.FullName -Destination $destPath -Force
            Write-Host "Copied: $relativePath" -ForegroundColor Green
            $CopiedCount++
        }
        
    } catch {
        Write-Host "Error copying $($file.FullName): $($_.Exception.Message)" -ForegroundColor Red
        $SkippedCount++
    }
}

Write-Host "`nCopy process completed!" -ForegroundColor Green
Write-Host "Files copied: $CopiedCount" -ForegroundColor Yellow
Write-Host "Files skipped: $SkippedCount" -ForegroundColor Yellow

# Display summary of what was copied
Write-Host "`nSummary of copied configuration files by directory:" -ForegroundColor Cyan
$GroupedFiles = $FilteredConfigFiles | Group-Object { Split-Path $_.DirectoryName -Leaf } | Sort-Object Name
foreach ($group in $GroupedFiles) {
    Write-Host "  $($group.Name): $($group.Count) files" -ForegroundColor White
}
