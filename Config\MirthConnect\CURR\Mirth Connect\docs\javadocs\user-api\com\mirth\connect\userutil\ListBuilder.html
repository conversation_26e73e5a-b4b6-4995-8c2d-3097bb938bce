<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:21 PDT 2023 -->
<title>ListBuilder</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ListBuilder";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/mirth/connect/userutil/JsonUtil.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/mirth/connect/userutil/Lists.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/mirth/connect/userutil/ListBuilder.html" target="_top">Frames</a></li>
<li><a href="ListBuilder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.userutil</div>
<h2 title="Class ListBuilder" class="title">Class ListBuilder</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.userutil.ListBuilder</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">ListBuilder</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></pre>
<div class="block">Convenience class to allow fluent building of lists.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#add-int-java.lang.Object-">add</a></span>(int&nbsp;index,
   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;element)</code>
<div class="block">Inserts the specified element at the specified position in this list
 (optional operation).</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#add-java.lang.Object-">add</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;e)</code>
<div class="block">Appends the specified element to the end of this list (optional
 operation).</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#addAll-java.util.Collection-">addAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&nbsp;c)</code>
<div class="block">Appends all of the elements in the specified collection to the end of
 this list, in the order that they are returned by the specified
 collection's iterator (optional operation).</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#addAll-int-java.util.Collection-">addAll</a></span>(int&nbsp;index,
      <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&nbsp;c)</code>
<div class="block">Inserts all of the elements in the specified collection into this
 list at the specified position (optional operation).</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#append-java.lang.Object-">append</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;e)</code>
<div class="block">Adds an element to the list using the <a href="../../../../com/mirth/connect/userutil/ListBuilder.html#add-java.lang.Object-"><code>add(java.lang.Object)</code></a> method, and returns this builder.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#clear--">clear</a></span>()</code>
<div class="block">Removes all of the elements from this list (optional operation).</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#contains-java.lang.Object-">contains</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;o)</code>
<div class="block">Returns <tt>true</tt> if this list contains the specified element.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#containsAll-java.util.Collection-">containsAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&nbsp;c)</code>
<div class="block">Returns <tt>true</tt> if this list contains all of the elements of the
 specified collection.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#equals-java.lang.Object-">equals</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;obj)</code>
<div class="block">Indicates whether some other object is "equal to" this one.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#get-int-">get</a></span>(int&nbsp;index)</code>
<div class="block">Returns the element at the specified position in this list.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#hashCode--">hashCode</a></span>()</code>
<div class="block">Returns a hash code value for the object.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#indexOf-java.lang.Object-">indexOf</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;o)</code>
<div class="block">Returns the index of the first occurrence of the specified element
 in this list, or -1 if this list does not contain the element.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#isEmpty--">isEmpty</a></span>()</code>
<div class="block">Returns <tt>true</tt> if this list contains no elements.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Iterator.html?is-external=true" title="class or interface in java.util">Iterator</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#iterator--">iterator</a></span>()</code>
<div class="block">Returns an iterator over the elements in this list in proper sequence.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#lastIndexOf-java.lang.Object-">lastIndexOf</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;o)</code>
<div class="block">Returns the index of the last occurrence of the specified element
 in this list, or -1 if this list does not contain the element.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListIterator.html?is-external=true" title="class or interface in java.util">ListIterator</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#listIterator--">listIterator</a></span>()</code>
<div class="block">Returns a list iterator over the elements in this list (in proper
 sequence).</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListIterator.html?is-external=true" title="class or interface in java.util">ListIterator</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#listIterator-int-">listIterator</a></span>(int&nbsp;index)</code>
<div class="block">Returns a list iterator over the elements in this list (in proper
 sequence), starting at the specified position in the list.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#remove-int-">remove</a></span>(int&nbsp;index)</code>
<div class="block">Removes the element at the specified position in this list (optional
 operation).</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#remove-java.lang.Object-">remove</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;o)</code>
<div class="block">Removes the first occurrence of the specified element from this list,
 if it is present (optional operation).</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#removeAll-java.util.Collection-">removeAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&nbsp;c)</code>
<div class="block">Removes from this list all of its elements that are contained in the
 specified collection (optional operation).</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#retainAll-java.util.Collection-">retainAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&nbsp;c)</code>
<div class="block">Retains only the elements in this list that are contained in the
 specified collection (optional operation).</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#set-int-java.lang.Object-">set</a></span>(int&nbsp;index,
   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;element)</code>
<div class="block">Replaces the element at the specified position in this list with the
 specified element (optional operation).</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#size--">size</a></span>()</code>
<div class="block">Returns the number of elements in this list.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#subList-int-int-">subList</a></span>(int&nbsp;fromIndex,
       int&nbsp;toIndex)</code>
<div class="block">Returns a view of the portion of this list between the specified
 <tt>fromIndex</tt>, inclusive, and <tt>toIndex</tt>, exclusive.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#toArray--">toArray</a></span>()</code>
<div class="block">Returns an array containing all of the elements in this list in proper
 sequence (from first to last element).</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#toArray-java.lang.Object:A-">toArray</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>[]&nbsp;a)</code>
<div class="block">Returns an array containing all of the elements in this list in
 proper sequence (from first to last element); the runtime type of
 the returned array is that of the specified array.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html#toString--">toString</a></span>()</code>
<div class="block">Returns a string representation of the object.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.List">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#replaceAll-java.util.function.UnaryOperator-" title="class or interface in java.util">replaceAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#sort-java.util.Comparator-" title="class or interface in java.util">sort</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#spliterator--" title="class or interface in java.util">spliterator</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.Collection">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.util.<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#parallelStream--" title="class or interface in java.util">parallelStream</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#removeIf-java.util.function.Predicate-" title="class or interface in java.util">removeIf</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#stream--" title="class or interface in java.util">stream</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Iterable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html?is-external=true#forEach-java.util.function.Consumer-" title="class or interface in java.lang">forEach</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="append-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>append</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a>&nbsp;append(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;e)</pre>
<div class="block">Adds an element to the list using the <a href="../../../../com/mirth/connect/userutil/ListBuilder.html#add-java.lang.Object-"><code>add(java.lang.Object)</code></a> method, and returns this builder.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - element to be appended to this list</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>This ListBuilder instance.</dd>
</dl>
</li>
</ul>
<a name="size--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>size</h4>
<pre>public&nbsp;int&nbsp;size()</pre>
<div class="block">Returns the number of elements in this list.  If this list contains
 more than <tt>Integer.MAX_VALUE</tt> elements, returns
 <tt>Integer.MAX_VALUE</tt>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#size--" title="class or interface in java.util">size</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#size--" title="class or interface in java.util">size</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the number of elements in this list</dd>
</dl>
</li>
</ul>
<a name="isEmpty--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isEmpty</h4>
<pre>public&nbsp;boolean&nbsp;isEmpty()</pre>
<div class="block">Returns <tt>true</tt> if this list contains no elements.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#isEmpty--" title="class or interface in java.util">isEmpty</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#isEmpty--" title="class or interface in java.util">isEmpty</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><tt>true</tt> if this list contains no elements</dd>
</dl>
</li>
</ul>
<a name="contains-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>contains</h4>
<pre>public&nbsp;boolean&nbsp;contains(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;o)</pre>
<div class="block">Returns <tt>true</tt> if this list contains the specified element.
 More formally, returns <tt>true</tt> if and only if this list contains
 at least one element <tt>e</tt> such that
 <tt>(o==null&nbsp;?&nbsp;e==null&nbsp;:&nbsp;o.equals(e))</tt>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#contains-java.lang.Object-" title="class or interface in java.util">contains</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#contains-java.lang.Object-" title="class or interface in java.util">contains</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>o</code> - element whose presence in this list is to be tested</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><tt>true</tt> if this list contains the specified element</dd>
</dl>
</li>
</ul>
<a name="iterator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>iterator</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Iterator.html?is-external=true" title="class or interface in java.util">Iterator</a>&nbsp;iterator()</pre>
<div class="block">Returns an iterator over the elements in this list in proper sequence.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html?is-external=true#iterator--" title="class or interface in java.lang">iterator</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Iterable.html?is-external=true" title="class or interface in java.lang">Iterable</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#iterator--" title="class or interface in java.util">iterator</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#iterator--" title="class or interface in java.util">iterator</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an iterator over the elements in this list in proper sequence</dd>
</dl>
</li>
</ul>
<a name="toArray--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toArray</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>[]&nbsp;toArray()</pre>
<div class="block">Returns an array containing all of the elements in this list in proper
 sequence (from first to last element).

 <p>The returned array will be "safe" in that no references to it are
 maintained by this list.  (In other words, this method must
 allocate a new array even if this list is backed by an array).
 The caller is thus free to modify the returned array.

 <p>This method acts as bridge between array-based and collection-based
 APIs.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#toArray--" title="class or interface in java.util">toArray</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#toArray--" title="class or interface in java.util">toArray</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing all of the elements in this list in proper
         sequence</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Arrays.html?is-external=true#asList-T...-" title="class or interface in java.util"><code>Arrays.asList(Object[])</code></a></dd>
</dl>
</li>
</ul>
<a name="toArray-java.lang.Object:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>toArray</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>[]&nbsp;toArray(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>[]&nbsp;a)</pre>
<div class="block">Returns an array containing all of the elements in this list in
 proper sequence (from first to last element); the runtime type of
 the returned array is that of the specified array.  If the list fits
 in the specified array, it is returned therein.  Otherwise, a new
 array is allocated with the runtime type of the specified array and
 the size of this list.

 <p>If the list fits in the specified array with room to spare (i.e.,
 the array has more elements than the list), the element in the array
 immediately following the end of the list is set to <tt>null</tt>.
 (This is useful in determining the length of the list <i>only</i> if
 the caller knows that the list does not contain any null elements.)

 <p>Like the <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#toArray--" title="class or interface in java.util"><code>List.toArray()</code></a> method, this method acts as bridge between
 array-based and collection-based APIs.  Further, this method allows
 precise control over the runtime type of the output array, and may,
 under certain circumstances, be used to save allocation costs.

 <p>Suppose <tt>x</tt> is a list known to contain only strings.
 The following code can be used to dump the list into a newly
 allocated array of <tt>String</tt>:

 <pre><code>
     String[] y = x.toArray(new String[0]);
 </code></pre>

 Note that <tt>toArray(new Object[0])</tt> is identical in function to
 <tt>toArray()</tt>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#toArray-T:A-" title="class or interface in java.util">toArray</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#toArray-T:A-" title="class or interface in java.util">toArray</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>a</code> - the array into which the elements of this list are to
          be stored, if it is big enough; otherwise, a new array of the
          same runtime type is allocated for this purpose.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>an array containing the elements of this list</dd>
</dl>
</li>
</ul>
<a name="add-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public&nbsp;boolean&nbsp;add(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;e)</pre>
<div class="block">Appends the specified element to the end of this list (optional
 operation).

 <p>Lists that support this operation may place limitations on what
 elements may be added to this list.  In particular, some
 lists will refuse to add null elements, and others will impose
 restrictions on the type of elements that may be added.  List
 classes should clearly specify in their documentation any restrictions
 on what elements may be added.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#add-E-" title="class or interface in java.util">add</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#add-E-" title="class or interface in java.util">add</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>e</code> - element to be appended to this list</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><tt>true</tt> (as specified by <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#add-E-" title="class or interface in java.util"><code>Collection.add(E)</code></a>)</dd>
</dl>
</li>
</ul>
<a name="remove-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public&nbsp;boolean&nbsp;remove(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;o)</pre>
<div class="block">Removes the first occurrence of the specified element from this list,
 if it is present (optional operation).  If this list does not contain
 the element, it is unchanged.  More formally, removes the element with
 the lowest index <tt>i</tt> such that
 <tt>(o==null&nbsp;?&nbsp;get(i)==null&nbsp;:&nbsp;o.equals(get(i)))</tt>
 (if such an element exists).  Returns <tt>true</tt> if this list
 contained the specified element (or equivalently, if this list changed
 as a result of the call).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#remove-java.lang.Object-" title="class or interface in java.util">remove</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#remove-java.lang.Object-" title="class or interface in java.util">remove</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>o</code> - element to be removed from this list, if present</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><tt>true</tt> if this list contained the specified element</dd>
</dl>
</li>
</ul>
<a name="containsAll-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>containsAll</h4>
<pre>public&nbsp;boolean&nbsp;containsAll(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&nbsp;c)</pre>
<div class="block">Returns <tt>true</tt> if this list contains all of the elements of the
 specified collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#containsAll-java.util.Collection-" title="class or interface in java.util">containsAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#containsAll-java.util.Collection-" title="class or interface in java.util">containsAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>c</code> - collection to be checked for containment in this list</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><tt>true</tt> if this list contains all of the elements of the
         specified collection</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#contains-java.lang.Object-" title="class or interface in java.util"><code>List.contains(Object)</code></a></dd>
</dl>
</li>
</ul>
<a name="addAll-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAll</h4>
<pre>public&nbsp;boolean&nbsp;addAll(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&nbsp;c)</pre>
<div class="block">Appends all of the elements in the specified collection to the end of
 this list, in the order that they are returned by the specified
 collection's iterator (optional operation).  The behavior of this
 operation is undefined if the specified collection is modified while
 the operation is in progress.  (Note that this will occur if the
 specified collection is this list, and it's nonempty.)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#addAll-java.util.Collection-" title="class or interface in java.util">addAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#addAll-java.util.Collection-" title="class or interface in java.util">addAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>c</code> - collection containing elements to be added to this list</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><tt>true</tt> if this list changed as a result of the call</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#add-E-" title="class or interface in java.util"><code>List.add(Object)</code></a></dd>
</dl>
</li>
</ul>
<a name="addAll-int-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAll</h4>
<pre>public&nbsp;boolean&nbsp;addAll(int&nbsp;index,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&nbsp;c)</pre>
<div class="block">Inserts all of the elements in the specified collection into this
 list at the specified position (optional operation).  Shifts the
 element currently at that position (if any) and any subsequent
 elements to the right (increases their indices).  The new elements
 will appear in this list in the order that they are returned by the
 specified collection's iterator.  The behavior of this operation is
 undefined if the specified collection is modified while the
 operation is in progress.  (Note that this will occur if the specified
 collection is this list, and it's nonempty.)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#addAll-int-java.util.Collection-" title="class or interface in java.util">addAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - index at which to insert the first element from the
              specified collection</dd>
<dd><code>c</code> - collection containing elements to be added to this list</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><tt>true</tt> if this list changed as a result of the call</dd>
</dl>
</li>
</ul>
<a name="removeAll-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>removeAll</h4>
<pre>public&nbsp;boolean&nbsp;removeAll(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&nbsp;c)</pre>
<div class="block">Removes from this list all of its elements that are contained in the
 specified collection (optional operation).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#removeAll-java.util.Collection-" title="class or interface in java.util">removeAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#removeAll-java.util.Collection-" title="class or interface in java.util">removeAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>c</code> - collection containing elements to be removed from this list</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><tt>true</tt> if this list changed as a result of the call</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#remove-java.lang.Object-" title="class or interface in java.util"><code>List.remove(Object)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#contains-java.lang.Object-" title="class or interface in java.util"><code>List.contains(Object)</code></a></dd>
</dl>
</li>
</ul>
<a name="retainAll-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>retainAll</h4>
<pre>public&nbsp;boolean&nbsp;retainAll(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&nbsp;c)</pre>
<div class="block">Retains only the elements in this list that are contained in the
 specified collection (optional operation).  In other words, removes
 from this list all of its elements that are not contained in the
 specified collection.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#retainAll-java.util.Collection-" title="class or interface in java.util">retainAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#retainAll-java.util.Collection-" title="class or interface in java.util">retainAll</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>c</code> - collection containing elements to be retained in this list</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><tt>true</tt> if this list changed as a result of the call</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#remove-java.lang.Object-" title="class or interface in java.util"><code>List.remove(Object)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#contains-java.lang.Object-" title="class or interface in java.util"><code>List.contains(Object)</code></a></dd>
</dl>
</li>
</ul>
<a name="clear--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clear</h4>
<pre>public&nbsp;void&nbsp;clear()</pre>
<div class="block">Removes all of the elements from this list (optional operation).
 The list will be empty after this call returns.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#clear--" title="class or interface in java.util">clear</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#clear--" title="class or interface in java.util">clear</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
</dl>
</li>
</ul>
<a name="get-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;get(int&nbsp;index)</pre>
<div class="block">Returns the element at the specified position in this list.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#get-int-" title="class or interface in java.util">get</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - index of the element to return</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the element at the specified position in this list</dd>
</dl>
</li>
</ul>
<a name="set-int-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>set</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;set(int&nbsp;index,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;element)</pre>
<div class="block">Replaces the element at the specified position in this list with the
 specified element (optional operation).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#set-int-E-" title="class or interface in java.util">set</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - index of the element to replace</dd>
<dd><code>element</code> - element to be stored at the specified position</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the element previously at the specified position</dd>
</dl>
</li>
</ul>
<a name="add-int-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>add</h4>
<pre>public&nbsp;void&nbsp;add(int&nbsp;index,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;element)</pre>
<div class="block">Inserts the specified element at the specified position in this list
 (optional operation).  Shifts the element currently at that position
 (if any) and any subsequent elements to the right (adds one to their
 indices).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#add-int-E-" title="class or interface in java.util">add</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - index at which the specified element is to be inserted</dd>
<dd><code>element</code> - element to be inserted</dd>
</dl>
</li>
</ul>
<a name="remove-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remove</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;remove(int&nbsp;index)</pre>
<div class="block">Removes the element at the specified position in this list (optional
 operation).  Shifts any subsequent elements to the left (subtracts one
 from their indices).  Returns the element that was removed from the
 list.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#remove-int-" title="class or interface in java.util">remove</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - the index of the element to be removed</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the element previously at the specified position</dd>
</dl>
</li>
</ul>
<a name="indexOf-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>indexOf</h4>
<pre>public&nbsp;int&nbsp;indexOf(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;o)</pre>
<div class="block">Returns the index of the first occurrence of the specified element
 in this list, or -1 if this list does not contain the element.
 More formally, returns the lowest index <tt>i</tt> such that
 <tt>(o==null&nbsp;?&nbsp;get(i)==null&nbsp;:&nbsp;o.equals(get(i)))</tt>,
 or -1 if there is no such index.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#indexOf-java.lang.Object-" title="class or interface in java.util">indexOf</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>o</code> - element to search for</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the index of the first occurrence of the specified element in
         this list, or -1 if this list does not contain the element</dd>
</dl>
</li>
</ul>
<a name="lastIndexOf-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lastIndexOf</h4>
<pre>public&nbsp;int&nbsp;lastIndexOf(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;o)</pre>
<div class="block">Returns the index of the last occurrence of the specified element
 in this list, or -1 if this list does not contain the element.
 More formally, returns the highest index <tt>i</tt> such that
 <tt>(o==null&nbsp;?&nbsp;get(i)==null&nbsp;:&nbsp;o.equals(get(i)))</tt>,
 or -1 if there is no such index.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#lastIndexOf-java.lang.Object-" title="class or interface in java.util">lastIndexOf</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>o</code> - element to search for</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the index of the last occurrence of the specified element in
         this list, or -1 if this list does not contain the element</dd>
</dl>
</li>
</ul>
<a name="listIterator--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>listIterator</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListIterator.html?is-external=true" title="class or interface in java.util">ListIterator</a>&nbsp;listIterator()</pre>
<div class="block">Returns a list iterator over the elements in this list (in proper
 sequence).</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#listIterator--" title="class or interface in java.util">listIterator</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a list iterator over the elements in this list (in proper
         sequence)</dd>
</dl>
</li>
</ul>
<a name="listIterator-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>listIterator</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListIterator.html?is-external=true" title="class or interface in java.util">ListIterator</a>&nbsp;listIterator(int&nbsp;index)</pre>
<div class="block">Returns a list iterator over the elements in this list (in proper
 sequence), starting at the specified position in the list.
 The specified index indicates the first element that would be
 returned by an initial call to <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListIterator.html?is-external=true#next--" title="class or interface in java.util"><code>next</code></a>.
 An initial call to <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListIterator.html?is-external=true#previous--" title="class or interface in java.util"><code>previous</code></a> would
 return the element with the specified index minus one.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#listIterator-int-" title="class or interface in java.util">listIterator</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - index of the first element to be returned from the
        list iterator (by a call to <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListIterator.html?is-external=true#next--" title="class or interface in java.util"><code>next</code></a>)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a list iterator over the elements in this list (in proper
         sequence), starting at the specified position in the list</dd>
</dl>
</li>
</ul>
<a name="subList-int-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>subList</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&nbsp;subList(int&nbsp;fromIndex,
                    int&nbsp;toIndex)</pre>
<div class="block">Returns a view of the portion of this list between the specified
 <tt>fromIndex</tt>, inclusive, and <tt>toIndex</tt>, exclusive.  (If
 <tt>fromIndex</tt> and <tt>toIndex</tt> are equal, the returned list is
 empty.)  The returned list is backed by this list, so non-structural
 changes in the returned list are reflected in this list, and vice-versa.
 The returned list supports all of the optional list operations supported
 by this list.<p>

 This method eliminates the need for explicit range operations (of
 the sort that commonly exist for arrays).  Any operation that expects
 a list can be used as a range operation by passing a subList view
 instead of a whole list.  For example, the following idiom
 removes a range of elements from a list:
 <pre><code>
      list.subList(from, to).clear();
 </code></pre>
 Similar idioms may be constructed for <tt>indexOf</tt> and
 <tt>lastIndexOf</tt>, and all of the algorithms in the
 <tt>Collections</tt> class can be applied to a subList.<p>

 The semantics of the list returned by this method become undefined if
 the backing list (i.e., this list) is <i>structurally modified</i> in
 any way other than via the returned list.  (Structural modifications are
 those that change the size of this list, or otherwise perturb it in such
 a fashion that iterations in progress may yield incorrect results.)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#subList-int-int-" title="class or interface in java.util">subList</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fromIndex</code> - low endpoint (inclusive) of the subList</dd>
<dd><code>toIndex</code> - high endpoint (exclusive) of the subList</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a view of the specified range within this list</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;obj)</pre>
<div class="block">Indicates whether some other object is "equal to" this one.
 <p>
 The <code>equals</code> method implements an equivalence relation
 on non-null object references:
 <ul>
 <li>It is <i>reflexive</i>: for any non-null reference value
     <code>x</code>, <code>x.equals(x)</code> should return
     <code>true</code>.
 <li>It is <i>symmetric</i>: for any non-null reference values
     <code>x</code> and <code>y</code>, <code>x.equals(y)</code>
     should return <code>true</code> if and only if
     <code>y.equals(x)</code> returns <code>true</code>.
 <li>It is <i>transitive</i>: for any non-null reference values
     <code>x</code>, <code>y</code>, and <code>z</code>, if
     <code>x.equals(y)</code> returns <code>true</code> and
     <code>y.equals(z)</code> returns <code>true</code>, then
     <code>x.equals(z)</code> should return <code>true</code>.
 <li>It is <i>consistent</i>: for any non-null reference values
     <code>x</code> and <code>y</code>, multiple invocations of
     <code>x.equals(y)</code> consistently return <code>true</code>
     or consistently return <code>false</code>, provided no
     information used in <code>equals</code> comparisons on the
     objects is modified.
 <li>For any non-null reference value <code>x</code>,
     <code>x.equals(null)</code> should return <code>false</code>.
 </ul>
 <p>
 The <code>equals</code> method for class <code>Object</code> implements
 the most discriminating possible equivalence relation on objects;
 that is, for any non-null reference values <code>x</code> and
 <code>y</code>, this method returns <code>true</code> if and only
 if <code>x</code> and <code>y</code> refer to the same object
 (<code>x == y</code> has the value <code>true</code>).
 <p>
 Note that it is generally necessary to override the <code>hashCode</code>
 method whenever this method is overridden, so as to maintain the
 general contract for the <code>hashCode</code> method, which states
 that equal objects must have equal hash codes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.util">equals</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.util">equals</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>obj</code> - the reference object with which to compare.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if this object is the same as the obj
          argument; <code>false</code> otherwise.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang"><code>Object.hashCode()</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/util/HashMap.html?is-external=true" title="class or interface in java.util"><code>HashMap</code></a></dd>
</dl>
</li>
</ul>
<a name="hashCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hashCode</h4>
<pre>public&nbsp;int&nbsp;hashCode()</pre>
<div class="block">Returns a hash code value for the object. This method is
 supported for the benefit of hash tables such as those provided by
 <a href="https://docs.oracle.com/javase/8/docs/api/java/util/HashMap.html?is-external=true" title="class or interface in java.util"><code>HashMap</code></a>.
 <p>
 The general contract of <code>hashCode</code> is:
 <ul>
 <li>Whenever it is invoked on the same object more than once during
     an execution of a Java application, the <code>hashCode</code> method
     must consistently return the same integer, provided no information
     used in <code>equals</code> comparisons on the object is modified.
     This integer need not remain consistent from one execution of an
     application to another execution of the same application.
 <li>If two objects are equal according to the <code>equals(Object)</code>
     method, then calling the <code>hashCode</code> method on each of
     the two objects must produce the same integer result.
 <li>It is <em>not</em> required that if two objects are unequal
     according to the <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang"><code>Object.equals(java.lang.Object)</code></a>
     method, then calling the <code>hashCode</code> method on each of the
     two objects must produce distinct integer results.  However, the
     programmer should be aware that producing distinct integer results
     for unequal objects may improve the performance of hash tables.
 </ul>
 <p>
 As much as is reasonably practical, the hashCode method defined by
 class <code>Object</code> does return distinct integers for distinct
 objects. (This is typically implemented by converting the internal
 address of the object into an integer, but this implementation
 technique is not required by the
 Java&trade; programming language.)</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true#hashCode--" title="class or interface in java.util">hashCode</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a></code></dd>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true#hashCode--" title="class or interface in java.util">hashCode</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a hash code value for this object.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang"><code>Object.equals(java.lang.Object)</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/System.html?is-external=true#identityHashCode-java.lang.Object-" title="class or interface in java.lang"><code>System.identityHashCode(java.lang.Object)</code></a></dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<div class="block">Returns a string representation of the object. In general, the
 <code>toString</code> method returns a string that
 "textually represents" this object. The result should
 be a concise but informative representation that is easy for a
 person to read.
 It is recommended that all subclasses override this method.
 <p>
 The <code>toString</code> method for class <code>Object</code>
 returns a string consisting of the name of the class of which the
 object is an instance, the at-sign character `<code>@</code>', and
 the unsigned hexadecimal representation of the hash code of the
 object. In other words, this method returns a string equal to the
 value of:
 <blockquote>
 <pre>
 getClass().getName() + '@' + Integer.toHexString(hashCode())
 </pre></blockquote></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a string representation of the object.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/mirth/connect/userutil/JsonUtil.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/mirth/connect/userutil/Lists.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/mirth/connect/userutil/ListBuilder.html" target="_top">Frames</a></li>
<li><a href="ListBuilder.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
