<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:21 PDT 2023 -->
<title>SMTPConnection</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SMTPConnection";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnectionFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/SMTPConnection.html" target="_top">Frames</a></li>
<li><a href="SMTPConnection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.server.userutil</div>
<h2 title="Class SMTPConnection" class="title">Class SMTPConnection</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.server.userutil.SMTPConnection</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SMTPConnection</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Used to send e-mail messages.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#SMTPConnection-java.lang.String-java.lang.String-boolean-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">SMTPConnection</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;port,
              boolean&nbsp;useAuthentication,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;secure,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;password,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;from)</code>
<div class="block">Instantiates an SMTP connection used to send e-mail messages with.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#SMTPConnection-java.lang.String-java.lang.String-int-boolean-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">SMTPConnection</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;port,
              int&nbsp;socketTimeout,
              boolean&nbsp;useAuthentication,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;secure,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;password,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;from)</code>
<div class="block">Instantiates an SMTP connection used to send e-mail messages with.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#getFrom--">getFrom</a></span>()</code>
<div class="block">Returns the FROM field being used for dispatched e-mail messages.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#getHost--">getHost</a></span>()</code>
<div class="block">Returns the SMTP server address.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#getPassword--">getPassword</a></span>()</code>
<div class="block">Returns the password being used to authenticate to the SMTP server.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#getPort--">getPort</a></span>()</code>
<div class="block">Returns the SMTP server port.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#getSecure--">getSecure</a></span>()</code>
<div class="block">Returns the encryption security layer being used for the SMTP connection (e.g "TLS" or
 "SSL").</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#getSocketTimeout--">getSocketTimeout</a></span>()</code>
<div class="block">Returns the socket connection timeout value in milliseconds.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#getUsername--">getUsername</a></span>()</code>
<div class="block">Returns the username being used to authenticate to the SMTP server.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#isUseAuthentication--">isUseAuthentication</a></span>()</code>
<div class="block">Returns true if authentication is needed for the SMTP server, otherwise returns false.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#send-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">send</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toList,
    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;ccList,
    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;subject,
    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;body)</code>
<div class="block">Sends an e-mail message.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#send-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">send</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toList,
    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;ccList,
    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;from,
    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;subject,
    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;body)</code>
<div class="block">Sends an e-mail message.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#send-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">send</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toList,
    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;ccList,
    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;from,
    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;subject,
    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;body,
    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;charset)</code>
<div class="block">Sends an e-mail message.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#setFrom-java.lang.String-">setFrom</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;from)</code>
<div class="block">Sets the FROM field to use for dispatched e-mail messages.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#setHost-java.lang.String-">setHost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host)</code>
<div class="block">Sets the SMTP server address.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#setPassword-java.lang.String-">setPassword</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;password)</code>
<div class="block">Sets the password to use to authenticate to the SMTP server.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#setPort-java.lang.String-">setPort</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;port)</code>
<div class="block">Sets the SMTP server port.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#setSecure-java.lang.String-">setSecure</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;secure)</code>
<div class="block">Sets the encryption security layer to use for the SMTP connection.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#setSocketTimeout-int-">setSocketTimeout</a></span>(int&nbsp;socketTimeout)</code>
<div class="block">Sets the socket connection timeout value.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#setUseAuthentication-boolean-">setUseAuthentication</a></span>(boolean&nbsp;useAuthentication)</code>
<div class="block">Sets whether authentication is needed for the SMTP server.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html#setUsername-java.lang.String-">setUsername</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username)</code>
<div class="block">Sets the username to use to authenticate to the SMTP server.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="SMTPConnection-java.lang.String-java.lang.String-int-boolean-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>SMTPConnection</h4>
<pre>public&nbsp;SMTPConnection(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;port,
                      int&nbsp;socketTimeout,
                      boolean&nbsp;useAuthentication,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;secure,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;password,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;from)</pre>
<div class="block">Instantiates an SMTP connection used to send e-mail messages with.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>host</code> - The SMTP server address.</dd>
<dd><code>port</code> - The SMTP server port (e.g. 25, 587, 465).</dd>
<dd><code>socketTimeout</code> - The socket connection timeout value in milliseconds.</dd>
<dd><code>useAuthentication</code> - Determines whether authentication is needed for the SMTP server.</dd>
<dd><code>secure</code> - The encryption security layer to use for the SMTP connection ("TLS" or "SSL"). If
            left blank, no encryption layer will be used.</dd>
<dd><code>username</code> - If authentication is required, the username to authenticate with.</dd>
<dd><code>password</code> - If authentication is required, the password to authenticate with.</dd>
<dd><code>from</code> - The FROM field to use for dispatched e-mail messages.</dd>
</dl>
</li>
</ul>
<a name="SMTPConnection-java.lang.String-java.lang.String-boolean-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>SMTPConnection</h4>
<pre>public&nbsp;SMTPConnection(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;port,
                      boolean&nbsp;useAuthentication,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;secure,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;password,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;from)</pre>
<div class="block">Instantiates an SMTP connection used to send e-mail messages with.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>host</code> - The SMTP server address.</dd>
<dd><code>port</code> - The SMTP server port (e.g. 25, 587, 465).</dd>
<dd><code>useAuthentication</code> - Determines whether authentication is needed for the SMTP server.</dd>
<dd><code>secure</code> - The encryption security layer to use for the SMTP connection ("TLS" or "SSL"). If
            left blank, no encryption layer will be used.</dd>
<dd><code>username</code> - If authentication is required, the username to authenticate with.</dd>
<dd><code>password</code> - If authentication is required, the password to authenticate with.</dd>
<dd><code>from</code> - The FROM field to use for the e-mail.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getHost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getHost()</pre>
<div class="block">Returns the SMTP server address.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The SMTP server address.</dd>
</dl>
</li>
</ul>
<a name="setHost-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHost</h4>
<pre>public&nbsp;void&nbsp;setHost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;host)</pre>
<div class="block">Sets the SMTP server address.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>host</code> - The SMTP server address to use.</dd>
</dl>
</li>
</ul>
<a name="getPort--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPort</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPort()</pre>
<div class="block">Returns the SMTP server port.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The SMTP server port.</dd>
</dl>
</li>
</ul>
<a name="setPort-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPort</h4>
<pre>public&nbsp;void&nbsp;setPort(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;port)</pre>
<div class="block">Sets the SMTP server port.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>port</code> - The SMTP server port to use (e.g. 25, 587, 465).</dd>
</dl>
</li>
</ul>
<a name="isUseAuthentication--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isUseAuthentication</h4>
<pre>public&nbsp;boolean&nbsp;isUseAuthentication()</pre>
<div class="block">Returns true if authentication is needed for the SMTP server, otherwise returns false.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if authentication is needed for the SMTP server, otherwise returns false.</dd>
</dl>
</li>
</ul>
<a name="setUseAuthentication-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUseAuthentication</h4>
<pre>public&nbsp;void&nbsp;setUseAuthentication(boolean&nbsp;useAuthentication)</pre>
<div class="block">Sets whether authentication is needed for the SMTP server.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>useAuthentication</code> - Determines whether authentication is needed for the SMTP server.</dd>
</dl>
</li>
</ul>
<a name="getSecure--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSecure</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getSecure()</pre>
<div class="block">Returns the encryption security layer being used for the SMTP connection (e.g "TLS" or
 "SSL").</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The encryption security layer being used for the SMTP connection (e.g "TLS" or
         "SSL").</dd>
</dl>
</li>
</ul>
<a name="setSecure-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSecure</h4>
<pre>public&nbsp;void&nbsp;setSecure(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;secure)</pre>
<div class="block">Sets the encryption security layer to use for the SMTP connection.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>secure</code> - The encryption security layer to use for the SMTP connection ("TLS" or "SSL"). If
            left blank, no encryption layer will be used.</dd>
</dl>
</li>
</ul>
<a name="getUsername--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUsername</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getUsername()</pre>
<div class="block">Returns the username being used to authenticate to the SMTP server.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The username being used to authenticate to the SMTP server.</dd>
</dl>
</li>
</ul>
<a name="setUsername-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUsername</h4>
<pre>public&nbsp;void&nbsp;setUsername(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;username)</pre>
<div class="block">Sets the username to use to authenticate to the SMTP server.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>username</code> - The username to authenticate with.</dd>
</dl>
</li>
</ul>
<a name="getPassword--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPassword</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPassword()</pre>
<div class="block">Returns the password being used to authenticate to the SMTP server.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The password being used to authenticate to the SMTP server.</dd>
</dl>
</li>
</ul>
<a name="setPassword-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPassword</h4>
<pre>public&nbsp;void&nbsp;setPassword(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;password)</pre>
<div class="block">Sets the password to use to authenticate to the SMTP server.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>password</code> - The password to authenticate with.</dd>
</dl>
</li>
</ul>
<a name="getFrom--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFrom</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getFrom()</pre>
<div class="block">Returns the FROM field being used for dispatched e-mail messages.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The FROM field being used for dispatched e-mail messages.</dd>
</dl>
</li>
</ul>
<a name="setFrom-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFrom</h4>
<pre>public&nbsp;void&nbsp;setFrom(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;from)</pre>
<div class="block">Sets the FROM field to use for dispatched e-mail messages.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>from</code> - The FROM field to use for dispatched e-mail messages.</dd>
</dl>
</li>
</ul>
<a name="getSocketTimeout--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSocketTimeout</h4>
<pre>public&nbsp;int&nbsp;getSocketTimeout()</pre>
<div class="block">Returns the socket connection timeout value in milliseconds.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The socket connection timeout value in milliseconds.</dd>
</dl>
</li>
</ul>
<a name="setSocketTimeout-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSocketTimeout</h4>
<pre>public&nbsp;void&nbsp;setSocketTimeout(int&nbsp;socketTimeout)</pre>
<div class="block">Sets the socket connection timeout value.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>socketTimeout</code> - The socket connection timeout value in milliseconds.</dd>
</dl>
</li>
</ul>
<a name="send-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>send</h4>
<pre>public&nbsp;void&nbsp;send(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toList,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;ccList,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;from,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;subject,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;body,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;charset)
          throws org.apache.commons.mail.EmailException</pre>
<div class="block">Sends an e-mail message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>toList</code> - A string representing a list of e-mail addresses to send the message to (separated
            by ",").</dd>
<dd><code>ccList</code> - A string representing a list of e-mail addresses to copy the message to (separated
            by ",").</dd>
<dd><code>from</code> - The FROM field to use for the e-mail message.</dd>
<dd><code>subject</code> - The subject of the e-mail message.</dd>
<dd><code>body</code> - The content of the e-mail message.</dd>
<dd><code>charset</code> - The charset encoding to use when sending the e-mail message.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.apache.commons.mail.EmailException</code> - If an error occurred while sending the e-mail message.</dd>
</dl>
</li>
</ul>
<a name="send-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>send</h4>
<pre>public&nbsp;void&nbsp;send(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toList,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;ccList,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;from,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;subject,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;body)
          throws org.apache.commons.mail.EmailException</pre>
<div class="block">Sends an e-mail message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>toList</code> - A string representing a list of e-mail addresses to send the message to (separated
            by ",").</dd>
<dd><code>ccList</code> - A string representing a list of e-mail addresses to copy the message to (separated
            by ",").</dd>
<dd><code>from</code> - The FROM field to use for the e-mail message.</dd>
<dd><code>subject</code> - The subject of the e-mail message.</dd>
<dd><code>body</code> - The content of the e-mail message.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.apache.commons.mail.EmailException</code> - If an error occurred while sending the e-mail message.</dd>
</dl>
</li>
</ul>
<a name="send-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>send</h4>
<pre>public&nbsp;void&nbsp;send(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toList,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;ccList,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;subject,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;body)
          throws org.apache.commons.mail.EmailException</pre>
<div class="block">Sends an e-mail message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>toList</code> - A string representing a list of e-mail addresses to send the message to (separated
            by ",").</dd>
<dd><code>ccList</code> - A string representing a list of e-mail addresses to copy the message to (separated
            by ",").</dd>
<dd><code>subject</code> - The subject of the e-mail message.</dd>
<dd><code>body</code> - The content of the e-mail message.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.apache.commons.mail.EmailException</code> - If an error occurred while sending the e-mail message.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnectionFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/SMTPConnection.html" target="_top">Frames</a></li>
<li><a href="SMTPConnection.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
