<connectorMetaData path="jms">
	<name>J<PERSON> Sender</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to write messages to a JMS queue.</description>
	<clientClassName>com.mirth.connect.connectors.jms.JmsSender</clientClassName>
	<serverClassName>com.mirth.connect.connectors.jms.JmsDispatcher</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.jms.JmsDispatcherProperties</sharedClassName>
	<library type="CLIENT" path="jms-client.jar" />
	<library type="SHARED" path="jms-shared.jar" />
	<library type="SERVER" path="jms-server.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.connectors.jms.JmsConnectorServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.connectors.jms.JmsConnectorServlet"/>
	<transformers></transformers>
	<protocol>jms</protocol>
	<type>DESTINATION</type>
</connectorMetaData>
