<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:21 PDT 2023 -->
<title>SerializerFactory</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="SerializerFactory";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":41,"i3":41,"i4":41,"i5":41,"i6":41,"i7":41,"i8":41,"i9":41,"i10":9,"i11":9,"i12":41};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/ResponseFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/SerializerFactory.html" target="_top">Frames</a></li>
<li><a href="SerializerFactory.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.server.userutil</div>
<h2 title="Class SerializerFactory" class="title">Class SerializerFactory</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.server.userutil.SerializerFactory</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">SerializerFactory</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Used to create a serializer for a specific data type for conversion to and from XML.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getDefaultDeserializationProperties-java.lang.String-">getDefaultDeserializationProperties</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dataType)</code>
<div class="block">Returns a map of default properties used to customize how deserialization from XML to the
 data type is performed.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getDefaultSerializationProperties-java.lang.String-">getDefaultSerializationProperties</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dataType)</code>
<div class="block">Returns a map of default properties used to customize how serialization from the data type to
 XML is performed.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static com.mirth.connect.model.converters.IMessageSerializer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getEDISerializer-java.lang.String-java.lang.String-java.lang.String-">getEDISerializer</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;segmentDelim,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;elementDelim,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;subelementDelim)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static com.mirth.connect.model.converters.IMessageSerializer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer--">getHL7Serializer</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static com.mirth.connect.model.converters.IMessageSerializer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer-boolean-boolean-">getHL7Serializer</a></span>(boolean&nbsp;useStrictParser,
                boolean&nbsp;useStrictValidation)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static com.mirth.connect.model.converters.IMessageSerializer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer-boolean-boolean-boolean-">getHL7Serializer</a></span>(boolean&nbsp;useStrictParser,
                boolean&nbsp;useStrictValidation,
                boolean&nbsp;handleRepetitions)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static com.mirth.connect.model.converters.IMessageSerializer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer-boolean-boolean-boolean-boolean-">getHL7Serializer</a></span>(boolean&nbsp;useStrictParser,
                boolean&nbsp;useStrictValidation,
                boolean&nbsp;handleRepetitions,
                boolean&nbsp;convertLFtoCR)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static com.mirth.connect.model.converters.IMessageSerializer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer-boolean-boolean-boolean-boolean-boolean-">getHL7Serializer</a></span>(boolean&nbsp;useStrictParser,
                boolean&nbsp;useStrictValidation,
                boolean&nbsp;handleRepetitions,
                boolean&nbsp;convertLFtoCR,
                boolean&nbsp;handleSubcomponents)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static com.mirth.connect.model.converters.IMessageSerializer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getNCPDPSerializer-java.lang.String-java.lang.String-java.lang.String-">getNCPDPSerializer</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;segmentDelim,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupDelim,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldDelim)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static com.mirth.connect.model.converters.IMessageSerializer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getNCPDPSerializer-java.lang.String-java.lang.String-java.lang.String-boolean-">getNCPDPSerializer</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;segmentDelim,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupDelim,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldDelim,
                  boolean&nbsp;useStrictValidation)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static com.mirth.connect.model.converters.IMessageSerializer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getSerializer-java.lang.String-">getSerializer</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dataType)</code>
<div class="block">Returns a serializer (with toXML and fromXML methods) for a given data type.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static com.mirth.connect.model.converters.IMessageSerializer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getSerializer-java.lang.String-java.util.Map-java.util.Map-">getSerializer</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dataType,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;serializationPropertiesMap,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;deserializationPropertiesMap)</code>
<div class="block">Returns a serializer (with toXML and fromXML methods) for a given data type and properties.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static com.mirth.connect.model.converters.IMessageSerializer</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html#getX12Serializer-boolean-">getX12Serializer</a></span>(boolean&nbsp;inferDelimiters)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getSerializer-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSerializer</h4>
<pre>public static&nbsp;com.mirth.connect.model.converters.IMessageSerializer&nbsp;getSerializer(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dataType)</pre>
<div class="block">Returns a serializer (with toXML and fromXML methods) for a given data type. Any
 serialization or deserialization properties will be left as the default values.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dataType</code> - The plugin point (e.g. "HL7V2") of the data type to create the serializer for.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instantiated IXMLSerializer object.</dd>
</dl>
</li>
</ul>
<a name="getSerializer-java.lang.String-java.util.Map-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSerializer</h4>
<pre>public static&nbsp;com.mirth.connect.model.converters.IMessageSerializer&nbsp;getSerializer(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dataType,
                                                                                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;serializationPropertiesMap,
                                                                                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;deserializationPropertiesMap)</pre>
<div class="block">Returns a serializer (with toXML and fromXML methods) for a given data type and properties.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dataType</code> - The plugin point (e.g. "HL7V2") of the data type to create the serializer for.</dd>
<dd><code>serializationPropertiesMap</code> - A Map of properties used to customize how serialization from the data type to XML
            is performed.</dd>
<dd><code>deserializationPropertiesMap</code> - A Map of properties used to customize how deserialization from XML to the data
            type is performed.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instantiated IXMLSerializer object.</dd>
</dl>
</li>
</ul>
<a name="getDefaultSerializationProperties-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultSerializationProperties</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getDefaultSerializationProperties(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dataType)</pre>
<div class="block">Returns a map of default properties used to customize how serialization from the data type to
 XML is performed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dataType</code> - The plugin point (e.g. "HL7V2") of the data type to get default properties for.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The map of default serialization properties.</dd>
</dl>
</li>
</ul>
<a name="getDefaultDeserializationProperties-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDefaultDeserializationProperties</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getDefaultDeserializationProperties(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;dataType)</pre>
<div class="block">Returns a map of default properties used to customize how deserialization from XML to the
 data type is performed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dataType</code> - The plugin point (e.g. "HL7V2") of the data type to get default properties for.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The map of default deserialization properties.</dd>
</dl>
</li>
</ul>
<a name="getHL7Serializer-boolean-boolean-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHL7Serializer</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public static&nbsp;com.mirth.connect.model.converters.IMessageSerializer&nbsp;getHL7Serializer(boolean&nbsp;useStrictParser,
                                                                                                 boolean&nbsp;useStrictValidation,
                                                                                                 boolean&nbsp;handleRepetitions,
                                                                                                 boolean&nbsp;convertLFtoCR,
                                                                                                 boolean&nbsp;handleSubcomponents)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
<div class="block">Returns an HL7 v2.x serializer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>useStrictParser</code> - If true, messages will be parsed based upon strict HL7 specifications.</dd>
<dd><code>useStrictValidation</code> - If true, messages will be validated using HL7 specifications (applies to Strict
            Parser only).</dd>
<dd><code>handleRepetitions</code> - If true, field repetitions will be parsed (applies to Non-Strict Parser only).</dd>
<dd><code>convertLFtoCR</code> - If true, line feeds (\n) will be converted to carriage returns (\r) automatically
            (applies to Non-Strict Parser only).</dd>
<dd><code>handleSubcomponents</code> - If true, subcomponents will be parsed (applies to Non-Strict Parser only).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instantiated IXMLSerializer object.</dd>
</dl>
</li>
</ul>
<a name="getHL7Serializer-boolean-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHL7Serializer</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public static&nbsp;com.mirth.connect.model.converters.IMessageSerializer&nbsp;getHL7Serializer(boolean&nbsp;useStrictParser,
                                                                                                 boolean&nbsp;useStrictValidation,
                                                                                                 boolean&nbsp;handleRepetitions,
                                                                                                 boolean&nbsp;convertLFtoCR)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
<div class="block">Returns an HL7 v2.x serializer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>useStrictParser</code> - If true, messages will be parsed based upon strict HL7 specifications.</dd>
<dd><code>useStrictValidation</code> - If true, messages will be validated using HL7 specifications (applies to Strict
            Parser only).</dd>
<dd><code>handleRepetitions</code> - If true, field repetitions will be parsed (applies to Non-Strict Parser only).</dd>
<dd><code>convertLFtoCR</code> - If true, line feeds (\n) will be converted to carriage returns (\r) automatically
            (applies to Non-Strict Parser only).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instantiated IXMLSerializer object.</dd>
</dl>
</li>
</ul>
<a name="getHL7Serializer-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHL7Serializer</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public static&nbsp;com.mirth.connect.model.converters.IMessageSerializer&nbsp;getHL7Serializer(boolean&nbsp;useStrictParser,
                                                                                                 boolean&nbsp;useStrictValidation,
                                                                                                 boolean&nbsp;handleRepetitions)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
<div class="block">Returns an HL7 v2.x serializer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>useStrictParser</code> - If true, messages will be parsed based upon strict HL7 specifications.</dd>
<dd><code>useStrictValidation</code> - If true, messages will be validated using HL7 specifications (applies to Strict
            Parser only).</dd>
<dd><code>handleRepetitions</code> - If true, field repetitions will be parsed (applies to Non-Strict Parser only).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instantiated IXMLSerializer object.</dd>
</dl>
</li>
</ul>
<a name="getHL7Serializer-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHL7Serializer</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public static&nbsp;com.mirth.connect.model.converters.IMessageSerializer&nbsp;getHL7Serializer(boolean&nbsp;useStrictParser,
                                                                                                 boolean&nbsp;useStrictValidation)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
<div class="block">Returns an HL7 v2.x serializer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>useStrictParser</code> - If true, messages will be parsed based upon strict HL7 specifications.</dd>
<dd><code>useStrictValidation</code> - If true, messages will be validated using HL7 specifications (applies to Strict
            Parser only).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instantiated IXMLSerializer object.</dd>
</dl>
</li>
</ul>
<a name="getHL7Serializer--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getHL7Serializer</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public static&nbsp;com.mirth.connect.model.converters.IMessageSerializer&nbsp;getHL7Serializer()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
<div class="block">Returns an HL7 v2.x serializer.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instantiated IXMLSerializer object.</dd>
</dl>
</li>
</ul>
<a name="getX12Serializer-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getX12Serializer</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public static&nbsp;com.mirth.connect.model.converters.IMessageSerializer&nbsp;getX12Serializer(boolean&nbsp;inferDelimiters)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
<div class="block">Returns an EDI / X12 serializer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inferDelimiters</code> - This property only applies to X12 messages. If checked, the delimiters are
            inferred from the incoming message and the delimiter properties will not be used.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instantiated IXMLSerializer object.</dd>
</dl>
</li>
</ul>
<a name="getEDISerializer-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEDISerializer</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public static&nbsp;com.mirth.connect.model.converters.IMessageSerializer&nbsp;getEDISerializer(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;segmentDelim,
                                                                                                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;elementDelim,
                                                                                                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;subelementDelim)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
<div class="block">Returns an EDI / X12 serializer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>segmentDelim</code> - Characters that delimit the segments in the message.</dd>
<dd><code>elementDelim</code> - Characters that delimit the elements in the message.</dd>
<dd><code>subelementDelim</code> - Characters that delimit the subelements in the message.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instantiated IXMLSerializer object.</dd>
</dl>
</li>
</ul>
<a name="getNCPDPSerializer-java.lang.String-java.lang.String-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getNCPDPSerializer</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public static&nbsp;com.mirth.connect.model.converters.IMessageSerializer&nbsp;getNCPDPSerializer(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;segmentDelim,
                                                                                                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupDelim,
                                                                                                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldDelim,
                                                                                                   boolean&nbsp;useStrictValidation)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
<div class="block">Returns an NCPDP serializer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>segmentDelim</code> - Characters that delimit the segments in the message.</dd>
<dd><code>groupDelim</code> - Characters that delimit the groups in the message.</dd>
<dd><code>fieldDelim</code> - Characters that delimit the fields in the message.</dd>
<dd><code>useStrictValidation</code> - Validates the NCPDP message against a schema.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instantiated IXMLSerializer object.</dd>
</dl>
</li>
</ul>
<a name="getNCPDPSerializer-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getNCPDPSerializer</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public static&nbsp;com.mirth.connect.model.converters.IMessageSerializer&nbsp;getNCPDPSerializer(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;segmentDelim,
                                                                                                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;groupDelim,
                                                                                                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fieldDelim)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
<div class="block">Returns an NCPDP serializer.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>segmentDelim</code> - Characters that delimit the segments in the message.</dd>
<dd><code>groupDelim</code> - Characters that delimit the groups in the message.</dd>
<dd><code>fieldDelim</code> - Characters that delimit the fields in the message.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The instantiated IXMLSerializer object.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/ResponseFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/SerializerFactory.html" target="_top">Frames</a></li>
<li><a href="SerializerFactory.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
