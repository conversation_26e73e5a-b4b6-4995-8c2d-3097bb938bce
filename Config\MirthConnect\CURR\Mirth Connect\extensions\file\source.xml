<connectorMetaData path="file">
	<name>File Reader</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to poll for files from a local or network file system.</description>
	<clientClassName>com.mirth.connect.connectors.file.FileReader</clientClassName>
	<serverClassName>com.mirth.connect.connectors.file.FileReceiver</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.file.FileReceiverProperties</sharedClassName>
	<templateClassName>com.mirth.connect.connectors.file.FileReaderCodeTemplatePlugin</templateClassName>
	<library type="CLIENT" path="file-client.jar" />
	<library type="SHARED" path="file-shared.jar" />
	<library type="SERVER" path="file-server.jar" />
	<library type="SERVER" path="lib/jcifs-ng-2.1.8.jar" />
	<library type="SERVER" path="lib/webdavclient4j-core-0.92.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.connectors.file.FileConnectorServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.connectors.file.FileConnectorServlet"/>
	<protocol>file</protocol>
	<type>SOURCE</type>
</connectorMetaData>
