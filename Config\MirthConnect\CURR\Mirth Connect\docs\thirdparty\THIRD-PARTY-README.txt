

Mirth Connect and Mirth Connect Command Line Interface include the following,
which is distributed under the terms of the Apache License version 2.0 (see
APACHE-LICENSE-2.0.txt):

	Amazon Ion Java [Mirth Connect only] (https://github.com/amzn/ion-java)
	Apache Commons
	Apache Derby 10.10.1.1 [Mirth Connect only]
	Apache Geronimo (only components needed for the JMS API) [Mirth Connect only]
	Apache HttpComponents 4.x
	Apache Log4j 1.2.16
	Apache PDFBox 1.8.4 [Mirth Connect only]
	Apache Standard Taglib 1.2.1 [Mirth Connect only]
	Apache Tomcat Jasper JSP Engine 8.5.70 [Mirth Connect only]
	Apache Tomcat Jasper Expression Language Impl 8.5.70 [Mirth Connect only]
	Apache Velocity Engine 2.2
	Apache Velocity Tools 3.0
	Apache XMLBeans [Mirth Connect only]
	AWS SDK v2 for Java [Mirth Connect only] (https://github.com/aws/aws-sdk-java-v2)
	Bean Validation API 1.1.0 (http://beanvalidation.org/)
	Error Prone Annotations 2.3.4 [Mirth Connect only] (https://github.com/google/error-prone)
	Fast Infoset Project 1.2.13 [Mirth Connect only] (https://github.com/javaee/metro-fi/tree/fastinfoset-project-1.2.13)
	Google Code libphonenumber [Mirth Connect only] (https://github.com/google/libphonenumber)
	Google Code Prettify [Mirth Connect only] (https://github.com/google/code-prettify)
	Guava 28.2 (https://code.google.com/p/guava-libraries)
	Guice 4.1 (https://github.com/google/guice)
	HikariCP 2.3.2 [Mirth Connect only]
	J2ObjC Annotations 1.3 [Mirth Connect only] (https://github.com/google/j2objc/)
	Jackson Project (https://github.com/FasterXML/jackson)
	Jasypt 1.7.1 [Mirth Connect only]
	Java Native Access (JNA) 4.5.2 [Mirth Connect only] (https://github.com/java-native-access/jna)
	Jetty 9.4.44 [Mirth Connect only]
	Joda-Time 2.2 [Mirth Connect only]
	JSR305 Annotations for Findbugs 3.0.2 [Mirth Connect only] (http://findbugs.sourceforge.net/)
	MyBatis 3.1.1 [Mirth Connect only]
	Netty 4.1.41 [Mirth Connect only]
	Not-Going-To-Be-Commons-SSL 0.3.18
	Quartz Scheduler 2.1.7 [Mirth Connect only]
	SQLite JDBC Driver 3.7.2 [Mirth Connect only] (https://bitbucket.org/xerial/sqlite-jdbc)
	StAXON 1.3 [Mirth Connect only] (https://github.com/beckchr/staxon)
	Swagger Core 2.0.10 (https://github.com/swagger-api/swagger-core)
	Swagger UI 2.2.10 (http://swagger.io/swagger-ui/)
	webdavclient4j 0.92 [Mirth Connect only] (http://sourceforge.net/projects/webdavclient4j/)
	Woodstox 5.0.3 [Mirth Connect only] (https://github.com/FasterXML/woodstox)
	Zip4j 1.3.3 (http://www.lingala.net/zip4j/)


--------------------------------------------------------------------------------


Mirth Connect includes modified versions of files from Zip4j, which is
distributed under the terms of the Apache License version 2.0 
(see APACHE-LICENSE-2.0.txt). The following files have changed:

	net/lingala/zip4j/unzip/Unzip.java
	net/lingala/zip4j/unzip/UnzipEngine.java
	
	Changes: Modified to protect against the Zip Slip vulnerability.


--------------------------------------------------------------------------------


Mirth Connect includes the following, which is distributed under the terms of
the MPL version 1.1 (see MPL-1.1.txt). The source code is also available under
the same license terms.

	dcm4che 2.0.29 (source code can be downloaded at: http://www.dcm4che.org/)
	DcmRcv and DcmSnd were modified to allow overriding of the network
	connections.

	HAPI 2.3 (source code can be downloaded at: 
	https://github.com/hapifhir/hapi-hl7v2)

	iText, a free Java-PDF library version 2.1.7 (source code can be downloaded 
	at:
	http://search.maven.org/remotecontent?filepath=com/lowagie/itext/2.1.7/itext
	-2.1.7-sources.jar)

	Javassist 3.26.0 (source code can be downloaded at: 
	http://www.csg.ci.i.u-tokyo.ac.jp/~chiba/javassist/)

	
--------------------------------------------------------------------------------


Mirth Connect includes the following, which is distributed under the terms of
the MPL version 2.0 (see MPL-2.0.txt). The source code is also available under
the same license terms.

	Mozilla Rhino 1.7.13 (source code can be downloaded at:
	https://developer.mozilla.org/en-US/docs/Rhino)


--------------------------------------------------------------------------------


Mirth Connect includes the following, which is distributed under the terms of
the Common Development and Distribution License version 1.1 (see CDDL-1.1.txt):

	Extended StAX API 1.8 (source code can be downloaded from:
	https://github.com/javaee/metro-stax-ex/tree/1.8) 

	GlassFish High Availability API 3.1.9 (source code can be downloaded from:
	https://github.com/javaee/glassfish-ha-api/tree/3.1.9)
	
	GlassFish Management API 3.2.1.b001 (source code can be downloaded from:
	https://github.com/javaee/gmbal-commons/tree/management-api-3.2.1-b001)

	GlassFish MBean Annotation Library 3.1.0.b001 (source code can be downloaded
	from: https://github.com/javaee/gmbal/tree/VERSION-3.1.0-b001)

	HK2 2.4.0 (source code can be downloaded from:
	https://hk2.java.net/2.4.0-b34/)
	
	IStack Commons Runtime 3.0.6 (source code can be downloaded from: 
	https://github.com/javaee/jaxb-istack-commons)
	
	Java API for XML Web Services API 2.3.0 (source code can be downloaded from:
	https://github.com/javaee/jax-ws-spec/tree/2.3.0/api)
	
	Java API for XML Web Services Runtime and Tools 2.3.0.2 (source code can be
	downloaded from:
	https://github.com/javaee/metro-jax-ws/tree/2.3.0.2/jaxws-ri)
	
	Java Persistence API 1.0 (source code can be downloaded from:
	https://glassfish.java.net/downloads/persistence/JavaPersistence.html)
	
	Java XML Bind API 2.4.0-b180725.0427 (source code can be
	downloaded from: https://github.com/javaee/jaxb-spec/tree
	/2.4.0-b180725.0427/jaxb-api)
	
	Java XML Bind Runtime 2.4.0-b180725.0644 (source code can be
	downloaded from: https://github.com/javaee/jaxb-v2/tree
	/2.4.0-b180725.0644/jaxb-ri)
	
	JavaBeans Activation Framework API 1.2.0 (source code can be downloaded
	from: https://github.com/javaee/activation/tree/JAF-1_2_0)
	
	JavaMail API 1.5.0 (source code can be downloaded from:
	http://search.maven.org/remotecontent?filepath=com/sun/mail/javax.mail/1.5.0
	/javax.mail-1.5.0-sources.jar)
	
	JAXB TXW Runtime 2.4.0-b180725.0427 (source code can be downloaded from:
	https://github.com/javaee/jaxb-v2/tree/master/jaxb-ri/txw)
	
	Jersey RESTful Web Services framework 2.22.1 (source code can be downloaded
	from: https://jersey.java.net/)
	
	JSON Processing API (source code can be downloaded from: 
	https://jsonp.java.net/)
	
	Mimepull 1.9.7 (source code can be downloaded from:
	https://mimepull.java.net/)
	
	SOAP with Attachment API for Java 1.4.0 (source code can be downloaded from:
	https://github.com/javaee/javax.xml.soap/tree/1.4.0)
	
	SOAP with Attachment API for Java Impl 1.0 (source code can be downloaded
	from: https://javaee.github.io/metro-saaj/)
	
	WS-Policy Implementation for Project Metro 2.7.2 (source code can be
	downloaded from: https://github.com/javaee/metro-policy/tree/policy-2.7.2)
	
	XML Stream Buffer 1.5.4 (source code can be downloaded from:
	https://github.com/javaee/metro-xmlstreambuffer/tree/streambuffer-1.5.4)


--------------------------------------------------------------------------------


Mirth Connect includes the following, which is distributed under the terms of
the Common Development and Distribution License version 1.0 (see CDDL-1.0.txt):

	Java Servlet API 3.1.0 (source code can be downloaded from: https://java.net
	/projects/glassfish/sources/svn/show/tags/javax.servlet-api-3.1.0)
	
	Jetty Apache JSP Implementation 9.4.44 (source code can be downloaded from:
	http://download.eclipse.org/jetty/)

	Swinglabs Wizard (source code can be downloaded from:
	https://java.net/projects/wizard)
	
	Java Common Annotations API 1.3 (source code can be downloaded from: 
	https://github.com/javaee/javax.annotation/tree/1.3)
	
	Web Services Metadata API / JSR-181 (source code can be downloaded from:
	https://mvnrepository.com/artifact/javax.jws/jsr181-api/1.0-MR1)


--------------------------------------------------------------------------------


Mirth Connect includes the following, which is distributed under the terms of
the Common Public License version 1.0 (see CPL-1.0.txt):

	Web Services Description Language for Java Toolkit (WSDL4J) (source code can
	be downloaded from: http://wsdl4j.sourceforge.net/)


--------------------------------------------------------------------------------


Mirth Connect includes the following, which is distributed under the terms of
the Artistic License version 1.0 (see ARTISTIC-LICENSE-1.0.txt):

	Display tag library (displaytag.org)
	
	
--------------------------------------------------------------------------------


Mirth Connect includes the following, which is distributed under the terms of
the OSGi Specification License, Version 1.0 (see OSGi-1.0.txt):

	OSGi Core Release 4.2.0 (source code can be downloaded from:
	https://www.osgi.org/release-4-version-4-2/)
	
	
--------------------------------------------------------------------------------


Mirth Connect includes the following software:

	Eclipse Java Development Tools 3.19.0

Licensing information for this software can be found in the about.html file
contained within the org.eclipse.jdt.ecj-3.19.0.jar file. This file
can be found within the Mirth Connect installation folder at:

	[Mirth Connect Install Folder]/server-lib/jetty/jsp/

Or in the Mirth Connect source code distribution, it can be found at:

	[Mirth Connect Server Source Folder]/lib/jetty/jsp/


--------------------------------------------------------------------------------


Mirth Connect includes the following software:

	Apache XmlBeans

Licensing information for this software can be found in the LICENSE.txt and
NOTICE.txt files contained within the xbean-fixed-2.4.0.jar file. This file can
be found within the Mirth Connect installation folder at:

	[Mirth Connect Install Folder]/extensions/ws/lib

Or in the Mirth Connect source code distribution, it can be found at:

	[Mirth Connect Server Source Folder]/lib/extensions/ws


--------------------------------------------------------------------------------


Mirth Connect includes the jTDS JDBC driver version 1.3.1. This library and its
use are covered by the LGPL version 2.1 (see LGPL-2.1.txt). The library source
code is available at http://jtds.sourceforge.net/. The following file has
been changed:

	net/sourceforge/jtds/ssl/TdsTlsOutputStream.java
	
	Changes: Fixes made to allow SSL/TLS connections. More information here:
	https://sourceforge.net/p/jtds/bugs/725/
	https://sourceforge.net/p/jtds/patches/129/#08be


--------------------------------------------------------------------------------


Mirth Connect includes the MySQL Connector/J JDBC Driver version 5.1.25. This 
library and its use are covered by the GPL version 2 with Oracle's Free and 
Open Source Software ("FOSS") License Exception (see 
MYSQL-CONNECTOR-LICENSE.txt). The library source code is available at 
http://mirrors.ibiblio.org/maven2/mysql/mysql-connector-java/5.1.25/src/.


--------------------------------------------------------------------------------


Mirth Connect includes SoapUI version 4.0.1. This library and its use are
covered by the LGPL version 2.1 (see LGPL-2.1.txt). The library source code is
available at http://www.soapui.org/.


--------------------------------------------------------------------------------


Mirth Connect includes the jcifs-ng SMB client library in Java version 2.1.8. This
library and its use are covered by the LGPL version 2.1 (see LGPL-2.1.txt). The
library source code is available at https://github.com/AgNO3/jcifs-ng/.
	
	
--------------------------------------------------------------------------------


Mirth Connect includes the Pdf-renderer library ([Mirth Connect Install
Folder]/extensions/pdfviewer/lib/PDFRenderer.jar). This library and its use are
covered by the LGPL version 2.1 (see LGPL-2.1.txt). The library source code is
available at https://java.net/projects/pdf-renderer/.


--------------------------------------------------------------------------------


Mirth Connect includes javaparser version 1.0.8. This library and its use are
covered by the LGPL version 2.1 (see LGPL-2.1.txt). The library source code is
available at https://code.google.com/p/javaparser/.


--------------------------------------------------------------------------------


Mirth Connect includes SwingLabs SwingX version 1.6.2. This library and its
use are covered by the LGPL version 2.1 (see LGPL-2.1.txt). The library source
code is available at https://swingx.java.net/.


--------------------------------------------------------------------------------


Mirth Connect includes openhtmltopdf version 1.0.9. This library and its use
are covered by the LGPL version 2.1 (see LGPL-2.1.txt). The library source code
is available at https://github.com/danfickle/openhtmltopdf/.


--------------------------------------------------------------------------------


The license below pertains to the jQuery JavaScript library, which is included
with Mirth Connect.

=================== Beginning of License Information ===================

Copyright 2013 jQuery Foundation and other contributors
http://jquery.com/

Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

=================== End of License Information ===================


--------------------------------------------------------------------------------


The license below pertains to Bootstrap, which is included with Mirth Connect.

=================== Beginning of License Information ===================

Copyright (c) 2011-2014 Twitter, Inc

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

=================== End of License Information ===================


--------------------------------------------------------------------------------


The license below pertains to the bootstrap-tokenfield library, which is
included with Mirth Connect.

=================== Beginning of License Information ===================

Copyright (c) 2013 by Sliptree
https://github.com/sliptree/bootstrap-tokenfield

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

=================== End of License Information ===================


--------------------------------------------------------------------------------


The license information below pertains to Flying Saucer version 9.0.1, which is
included with Mirth Connect. This library and its use are covered by the LGPL
version 2.1 (see LGPL-2.1.txt) and its source code is available at
https://github.com/flyingsaucerproject/flyingsaucer or
https://code.google.com/p/flying-saucer/.

=================== Beginning of License Information ===================

FLYING SAUCER XML/CSS RENDERER LICENSE COMMENTS
https://xhtmlrenderer.dev.java.net/

All source code to Flying Saucer itself is licensed under the GNU Lesser General
Public License (LGPL); you can redistribute it and/or modify it under the terms
of the GNU Lesser General Public License as published by the Free Software
Foundation, either version 2.1 of the License, or (at your option) any later version.
A copy of the LGPL can be found
on the website of the Free Software Foundation, at 
http://www.gnu.org/copyleft/lesser.html, and in our distributions under
LICENSE-LGPL-2.1.txt.

Flying Saucer relies on several other free or open source projects in 
order to build and run. Where binary Java JAR files are included, we include
only the unmodified binary releases as provided by those other projects. 
Source code for the respective projects can be found on the project 
websites, listed below.

Java projects that are used for building and running Flying Saucer are:


JUnit (for testing)
http://www.junit.org/index.htm
License: Common Public License Version 1.0
Using version 3.8.1
Included as lib/junit.jar

Ant (for building)
http://ant.apache.org/
License: Apache Software License Version 2.0
Not packaged with release; development using version 1.6.x

iText (PDF generation)
http://www.lowagie.com/iText/ and http://itextpdf.sourceforge.net/
License: Mozilla Public License Version 1.1
Using version 2.0.8.
Included as lib/iText-2.0.8.jar

SVGSalamander (SVG rendering in demo)
https://svgsalamander.dev.java.net/
License: LGPL
Using version 1, released on the project website

SWT (Standard Widget Toolkit)
http://www.eclipse.org/swt/
License: Eclipse Public License Version 1.0 ("EPL"). A copy of the EPL is available at http://www.eclipse.org/legal/epl-v10.html
Including version 3.5 libraries for Windows, Mac, and Linux

DocBook CSS (DocBook XML Rendering with CSS)
  MozBook CSS (public domain, released by David Horton)
  WSIWYGDocBook 1.01 -- see demos\docbook\wysiwygdocbook1.01, and COPYING therein
  docbook-css-0.4 -- see demos\docbook\docbook-css-0.4, and COPYING therein

W3C CSS Test Suite
  Distributed with our source bundle for the convenience of our developers.
  License is W3C Document License, see LICENSE_W3C_TEST.
  Source is http://www.w3.org/Style/CSS/Test/, for the most current version
  please see that URL.

Special thanks to Andy Streich, et. al. for Xilize
Xilize Text to HTML library
http://xilize.sourceforge.net/
License: GPL
Used version 3.x, only to produce documentation
Shipped with our source bundle as a convenience for developers rebuilding 
documentation; we do not use nor link to the Xilize libraries at runtime

BeanShell
http://www.beanshell.org
License: LGPL (dual-license with SPL)
Use version 2.x as Xilize has a dependency on it; used only to produce 
documentation.

XML-APIs (extracted from Apache Xerces-2)
http://xerces.apache.org/xerces2-j/
License: Apache v2
We include the xml-apis.jar from the Xerces binary distribution in order to allow
our code to compile on JDK 1.4, which does not include newer XML APIs, even though
these API implementations will run on version 1.4 of the JRE. The JAR is unmodified
from the Xerces release, but is renamed as xml-apis-xerces-2.9.1.jar to
make the version clear.
Included as lib/xml-apis-xerces-2.9.1.jar

=================== End of License Information ===================


--------------------------------------------------------------------------------


The license below pertains to Bouncy Castle Crypto, which is included with Mirth
Connect and Mirth Connect Command Line Interface.

=================== Beginning of License ===================

Copyright (c) 2000 - 2013 The Legion Of The Bouncy Castle
(http://www.bouncycastle.org)

Permission is hereby granted, free of charge, to any person obtaining a copy of
this software and associated documentation files (the "Software"), to deal in
the Software without restriction, including without limitation the rights to
use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
the Software, and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to MigLayout version 4.2, which is included with
Mirth Connect.

=================== Beginning of License ===================

 License (BSD):
 ==============
 
 Copyright (c) 2004, Mikael Grev, MiG InfoCom AB. (miglayout (at) miginfocom (dot) com)
 All rights reserved.

 Redistribution and use in source and binary forms, with or without modification,
 are permitted provided that the following conditions are met:
 Redistributions of source code must retain the above copyright notice, this list
 of conditions and the following disclaimer.
 Redistributions in binary form must reproduce the above copyright notice, this
 list of conditions and the following disclaimer in the documentation and/or other
 materials provided with the distribution.
 Neither the name of the MiG InfoCom AB nor the names of its contributors may be
 used to endorse or promote products derived from this software without specific
 prior written permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
 OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
 OF SUCH DAMAGE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to JDOM version 1.1.1, which is included with Mirth
Connect.

=================== Beginning of License ===================

/*-- 

 $Id: LICENSE.txt,v 1.11 2004/02/06 09:32:57 jhunter Exp $

 Copyright (C) 2000-2004 Jason Hunter & Brett McLaughlin.
 All rights reserved.
 
 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions
 are met:
 
 1. Redistributions of source code must retain the above copyright
    notice, this list of conditions, and the following disclaimer.
 
 2. Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions, and the disclaimer that follows 
    these conditions in the documentation and/or other materials 
    provided with the distribution.

 3. The name "JDOM" must not be used to endorse or promote products
    derived from this software without prior written permission.  For
    written permission, please contact <request_AT_jdom_DOT_org>.
 
 4. Products derived from this software may not be called "JDOM", nor
    may "JDOM" appear in their name, without prior written permission
    from the JDOM Project Management <request_AT_jdom_DOT_org>.
 
 In addition, we request (but do not require) that you include in the 
 end-user documentation provided with the redistribution and/or in the 
 software itself an acknowledgement equivalent to the following:
     "This product includes software developed by the
      JDOM Project (http://www.jdom.org/)."
 Alternatively, the acknowledgment may be graphical using the logos 
 available at http://www.jdom.org/images/logos.

 THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 DISCLAIMED.  IN NO EVENT SHALL THE JDOM AUTHORS OR THE PROJECT
 CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 SUCH DAMAGE.

 This software consists of voluntary contributions made by many 
 individuals on behalf of the JDOM Project and was originally 
 created by Jason Hunter <jhunter_AT_jdom_DOT_org> and
 Brett McLaughlin <brett_AT_jdom_DOT_org>.  For more information
 on the JDOM Project, please see <http://www.jdom.org/>. 

 */

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to MXP1: Xml Pull Parser 3rd Edition (XPP3) version
1.1.4c, which is included with Mirth Connect and Mirth Connect Command Line
Interface.

=================== Beginning of License ===================

Indiana University Extreme! Lab Software License

Version 1.1.1

Copyright (c) 2002 Extreme! Lab, Indiana University. All rights reserved.

Redistribution and use in source and binary forms, with or without 
modification, are permitted provided that the following conditions 
are met:

1. Redistributions of source code must retain the above copyright notice, 
   this list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright 
   notice, this list of conditions and the following disclaimer in 
   the documentation and/or other materials provided with the distribution.

3. The end-user documentation included with the redistribution, if any, 
   must include the following acknowledgment:

  "This product includes software developed by the Indiana University 
  Extreme! Lab (http://www.extreme.indiana.edu/)."

Alternately, this acknowledgment may appear in the software itself, 
if and wherever such third-party acknowledgments normally appear.

4. The names "Indiana Univeristy" and "Indiana Univeristy Extreme! Lab" 
must not be used to endorse or promote products derived from this 
software without prior written permission. For written permission, 
please contact http://www.extreme.indiana.edu/.

5. Products derived from this software may not use "Indiana Univeristy" 
name nor may "Indiana Univeristy" appear in their name, without prior 
written permission of the Indiana University.

THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
IN NO EVENT SHALL THE AUTHORS, COPYRIGHT HOLDERS OR ITS CONTRIBUTORS
BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to OGNL (Object-Graph Navigation Language), which is
included with Mirth Connect within the MyBatis 3.1.1 library.

=================== Beginning of License ===================

//--------------------------------------------------------------------------
//  Copyright (c) 2004, Drew Davidson and Luke Blanshard
//  All rights reserved.
//
//  Redistribution and use in source and binary forms, with or without
//  modification, are permitted provided that the following conditions are
//  met:
//
//  Redistributions of source code must retain the above copyright notice,
//  this list of conditions and the following disclaimer.
//  Redistributions in binary form must reproduce the above copyright
//  notice, this list of conditions and the following disclaimer in the
//  documentation and/or other materials provided with the distribution.
//  Neither the name of the Drew Davidson nor the names of its contributors
//  may be used to endorse or promote products derived from this software
//  without specific prior written permission.
//
//  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
//  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
//  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
//  FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
//  COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
//  INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
//  BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
//  OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
//  AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
//  OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
//  THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
//  DAMAGE.
//--------------------------------------------------------------------------

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to the PostgreSQL JDBC Driver version 9.4 build 1200, which is included with Mirth Connect.

=================== Beginning of License ===================

Copyright (c) 1997-2011, PostgreSQL Global Development Group
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice,
   this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright notice,
   this list of conditions and the following disclaimer in the documentation
   and/or other materials provided with the distribution.
3. Neither the name of the PostgreSQL Global Development Group nor the names
   of its contributors may be used to endorse or promote products derived
   from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to XStream version 1.4.4, which is included with
Mirth Connect and Mirth Connect Command Line Interface.

=================== Beginning of License ===================

Copyright (c) 2003-2006, Joe Walnes
Copyright (c) 2006-2009, 2011 XStream Committers
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this list of
conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice, this list of
conditions and the following disclaimer in the documentation and/or other materials provided
with the distribution.

3. Neither the name of XStream nor the names of its contributors may be used to endorse
or promote products derived from this software without specific prior written
permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY
EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED
TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY
WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
DAMAGE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to Simple Logging Facade for Java (SLF4J) version
1.7.28, which is included with Mirth Connect.

=================== Beginning of License ===================

Copyright (c) 2004-2017 QOS.ch
All rights reserved.

Permission is hereby granted, free  of charge, to any person obtaining
a  copy  of this  software  and  associated  documentation files  (the
"Software"), to  deal in  the Software without  restriction, including
without limitation  the rights to  use, copy, modify,  merge, publish,
distribute,  sublicense, and/or sell  copies of  the Software,  and to
permit persons to whom the Software  is furnished to do so, subject to
the following conditions:

The  above  copyright  notice  and  this permission  notice  shall  be
included in all copies or substantial portions of the Software.

THE  SOFTWARE IS  PROVIDED  "AS  IS", WITHOUT  WARRANTY  OF ANY  KIND,
EXPRESS OR  IMPLIED, INCLUDING  BUT NOT LIMITED  TO THE  WARRANTIES OF
MERCHANTABILITY,    FITNESS    FOR    A   PARTICULAR    PURPOSE    AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE,  ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to Apache Xerces2 Java (Build Tools) version 2.9.1,
which is included with Mirth Connect.

=================== Beginning of License ===================

/*
 * The Apache Software License, Version 1.1
 *
 *
 * Copyright (c) 1999-2002 The Apache Software Foundation.  All rights 
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer. 
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution,
 *    if any, must include the following acknowledgment:  
 *       "This product includes software developed by the
 *        Apache Software Foundation (http://www.apache.org/)."
 *    Alternately, this acknowledgment may appear in the software itself,
 *    if and wherever such third-party acknowledgments normally appear.
 *
 * 4. The names "Xerces" and "Apache Software Foundation" must
 *    not be used to endorse or promote products derived from this
 *    software without prior written permission. For written 
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "Apache",
 *    nor may "Apache" appear in their name, without prior written
 *    permission of the Apache Software Foundation.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE APACHE SOFTWARE FOUNDATION OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Apache Software Foundation and was
 * originally based on software copyright (c) 1999, International
 * Business Machines, Inc., http://www.ibm.com.  For more
 * information on the Apache Software Foundation, please see
 * <http://www.apache.org/>.
 */

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to Jsch version 0.1.55, which is included with Mirth
Connect.

=================== Beginning of License ===================

JSch 0.0.* was released under the GNU LGPL license.  Later, we have switched 
over to a BSD-style license. 

------------------------------------------------------------------------------
Copyright (c) 2002-2012 Atsuhiko Yamanaka, JCraft,Inc. 
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

  1. Redistributions of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.

  2. Redistributions in binary form must reproduce the above copyright 
     notice, this list of conditions and the following disclaimer in 
     the documentation and/or other materials provided with the distribution.

  3. The names of the authors may not be used to endorse or promote products
     derived from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED WARRANTIES,
INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND
FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL JCRAFT,
INC. OR ANY CONTRIBUTORS TO THIS SOFTWARE BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA,
OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to JUnit 4.8.1, which is included with Mirth Connect.

=================== Beginning of License ===================

BSD License

Copyright (c) 2000-2006, www.hamcrest.org
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

Redistributions of source code must retain the above copyright notice, this list of
conditions and the following disclaimer. Redistributions in binary form must reproduce
the above copyright notice, this list of conditions and the following disclaimer in
the documentation and/or other materials provided with the distribution.

Neither the name of Hamcrest nor the names of its contributors may be used to endorse
or promote products derived from this software without specific prior written
permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY
EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT
SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED
TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR
BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY
WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
DAMAGE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to JGoodies Looks version 2.3.1, which is included
with Mirth Connect.

=================== Beginning of License ===================

              The BSD License for the JGoodies Looks
              ======================================

Copyright (c) 2001-2009 JGoodies Karsten Lentzsch. All rights reserved.

Redistribution and use in source and binary forms, with or without 
modification, are permitted provided that the following conditions are met:

 o Redistributions of source code must retain the above copyright notice, 
   this list of conditions and the following disclaimer. 
    
 o Redistributions in binary form must reproduce the above copyright notice, 
   this list of conditions and the following disclaimer in the documentation 
   and/or other materials provided with the distribution. 
    
 o Neither the name of JGoodies Karsten Lentzsch nor the names of 
   its contributors may be used to endorse or promote products derived 
   from this software without specific prior written permission. 
    
THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" 
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, 
THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR 
PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, 
EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, 
PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; 
OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, 
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR 
OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, 
EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to a portion of the software included with Mozilla
Rhino 1.7.13 (most of the classes in the
org.mozilla.javascript.v8dtoa package), which is included with Mirth Connect.

=================== Beginning of License ===================

Copyright 2010 the V8 project authors. All rights reserved.
Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:

    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above
      copyright notice, this list of conditions and the following
      disclaimer in the documentation and/or other materials provided
      with the distribution.
    * Neither the name of Google Inc. nor the names of its
      contributors may be used to endorse or promote products derived
      from this software without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to a portion of the software included with Mozilla
Rhino 1.7.13 (the org.mozilla.javascript.DToA class), which is
included with Mirth Connect.

=================== Beginning of License ===================

/****************************************************************
  *
  * The author of this software is David M. Gay.
  *
  * Copyright (c) 1991, 2000, 2001 by Lucent Technologies.
  *
  * Permission to use, copy, modify, and distribute this software for any
  * purpose without fee is hereby granted, provided that this entire notice
  * is included in all copies of any software which is or includes a copy
  * or modification of this software and in all copies of the supporting
  * documentation for such software.
  *
  * THIS SOFTWARE IS BEING PROVIDED "AS IS", WITHOUT ANY EXPRESS OR IMPLIED
  * WARRANTY.  IN PARTICULAR, NEITHER THE AUTHOR NOR LUCENT MAKES ANY
  * REPRESENTATION OR WARRANTY OF ANY KIND CONCERNING THE MERCHANTABILITY
  * OF THIS SOFTWARE OR ITS FITNESS FOR ANY PARTICULAR PURPOSE.
  *
  ***************************************************************/

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to a portion of the software included with Mozilla
Rhino 1.7.13 (the classes in the
org.mozilla.javascript.tools.debugger.treetable package), which is included with
Mirth Connect.

=================== Beginning of License ===================

 * Copyright 1997, 1998 Sun Microsystems, Inc.  All Rights Reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 *   - Redistributions of source code must retain the above copyright
 *     notice, this list of conditions and the following disclaimer.
 *
 *   - Redistributions in binary form must reproduce the above copyright
 *     notice, this list of conditions and the following disclaimer in the
 *     documentation and/or other materials provided with the distribution.
 *
 *   - Neither the name of Sun Microsystems nor the names of its
 *     contributors may be used to endorse or promote products derived
 *     from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
 * IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 * PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 * LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to a portion of the software included with the SQLite
JDBC Driver 3.7.2, which is included with Mirth Connect.

=================== Beginning of License ===================

Copyright (c) 2006, David Crawshaw.  All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions
are met:

1. Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.
2. Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS ``AS IS'' AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
SUCH DAMAGE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to a portion of the software included with
RSyntaxTextArea, which is included with Mirth Connect.

=================== Beginning of License ===================

Copyright (c) 2012, Robert Futrell
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:
    * Redistributions of source code must retain the above copyright
      notice, this list of conditions and the following disclaimer.
    * Redistributions in binary form must reproduce the above copyright
      notice, this list of conditions and the following disclaimer in the
      documentation and/or other materials provided with the distribution.
    * Neither the name of the author nor the names of its contributors may
      be used to endorse or promote products derived from this software
      without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
(INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to a portion of the software included with ASM, which
is included with Mirth Connect.

=================== Beginning of License ===================

 ASM: a very small and fast Java bytecode manipulation framework
 Copyright (c) 2000-2011 INRIA, France Telecom
 All rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions
 are met:
 1. Redistributions of source code must retain the above copyright
    notice, this list of conditions and the following disclaimer.
 2. Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the
    documentation and/or other materials provided with the distribution.
 3. Neither the name of the copyright holders nor the names of its
    contributors may be used to endorse or promote products derived from
    this software without specific prior written permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
 LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 THE POSSIBILITY OF SUCH DAMAGE.


=================== End of License =========================


--------------------------------------------------------------------------------


The license below pertains to JS Beautifier 1.6.8, which is included with Mirth
Connect.

=================== Beginning of License Information ===================

The MIT License (MIT)

Copyright (c) 2007-2017 Einar Lielmanis, Liam Newman, and contributors.

Permission is hereby granted, free of charge, to any person
obtaining a copy of this software and associated documentation files
(the "Software"), to deal in the Software without restriction,
including without limitation the rights to use, copy, modify, merge,
publish, distribute, sublicense, and/or sell copies of the Software,
and to permit persons to whom the Software is furnished to do so,
subject to the following conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

=================== End of License Information ===================


--------------------------------------------------------------------------------


The license below pertains to Stax2 API, which is included with Mirth Connect.

=================== Beginning of License Information ===================

Copyright (c) 2014, FasterXML, LLC

Redistribution and use in source and binary forms, with or without 
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, 
this list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice, 
this list of conditions and the following disclaimer in the documentation and/or 
other materials provided with the distribution.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND 
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED 
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. 
IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, 
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, 
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, 
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF 
LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE 
OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED 
OF THE POSSIBILITY OF SUCH DAMAGE.

=================== End of License Information ===================


--------------------------------------------------------------------------------


Mirth Connect includes unmodified libraries from OpenJFX, which is licensed 
under the GNU General Public License version 2, with the Classpath Exception.
A copy of this license is available in the file named GPLv2+CE.txt.

The source code for OpenJFX is available at: http://jdk.java.net/openjfx/


--------------------------------------------------------------------------------


Mirth Connect includes the OSHI library, which is licensed under the Eclipse 
Public License version 1.0. A copy of this license is available in the file
named EPL-1.0.txt.

The source code for OSHI is available at: https://github.com/oshi/oshi


--------------------------------------------------------------------------------


Mirth Connect includes the JaCoCo Java Code Coverage Library version 0.8.2,
which is licensed under the Eclipse Public License version 1.0. A copy of this
license is available in the file named EPL-1.0.txt.

The source code for the JaCoCo Java Code Coverage Library is available at
https://github.com/jacoco/jacoco/.


--------------------------------------------------------------------------------


The license below pertains to the args4j library, which is
included with Mirth Connect.

=================== Beginning of License Information ===================

Copyright (c) 2003-2016 by Kohsuke Kawaguchi
https://github.com/kohsuke/args4j

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

=================== End of License Information ===================


--------------------------------------------------------------------------------


The license below pertains to the Checker Qual annotations library, which is
included with Mirth Connect.

=================== Beginning of License Information ===================

The Checker Framework
Copyright 2004-present by the Checker Framework developers

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.

=================== End of License Information ===================


--------------------------------------------------------------------------------


The license below pertains to the Microsoft JDBC Driver for SQL Server, which is
included with Mirth Connect.

=================== Beginning of License Information ===================

﻿Copyright(c) 2020 Microsoft Corporation
All rights reserved.

MIT License
Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files(the "Software"), 
to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, 
and / or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions :

The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED *AS IS*, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, 
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER 
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS 
IN THE SOFTWARE.

=================== End of License Information ===================


--------------------------------------------------------------------------------