<pluginMetaData path="datapruner">
	<name>Data Pruner</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This plugin provides data pruning capability for Mirth Connect</description>
	<serverClasses>
		<string>com.mirth.connect.plugins.datapruner.DataPrunerService</string>
	</serverClasses>
	<clientClasses>
		<string>com.mirth.connect.plugins.datapruner.DataPrunerClient</string>
	</clientClasses>
	<library type="CLIENT" path="datapruner-client.jar" />
	<library type="SHARED" path="datapruner-shared.jar" />
	<library type="SERVER" path="datapruner-server.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.plugins.datapruner.DataPrunerServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.plugins.datapruner.DataPrunerServlet"/>
</pluginMetaData>
