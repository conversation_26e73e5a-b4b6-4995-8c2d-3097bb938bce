# PowerShell script to set up necessary directories for MirthConnect to run from Config folder
# Author: Augment Agent
# Date: 2025-07-05

param(
    [string]$ConfigRoot = ".\Config\MirthConnect\CURR\Mirth Connect"
)

Write-Host "Setting up MirthConnect directories in Config folder..." -ForegroundColor Green
Write-Host "Config Root: $ConfigRoot" -ForegroundColor Yellow

# Define required directories that MirthConnect needs
$RequiredDirectories = @(
    "logs",
    "webapps",
    "public_html",
    "public_api_html"
)

# Create required directories if they don't exist
foreach ($dir in $RequiredDirectories) {
    $dirPath = Join-Path $ConfigRoot $dir
    if (!(Test-Path $dirPath)) {
        New-Item -ItemType Directory -Path $dirPath -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "Directory already exists: $dir" -ForegroundColor Gray
    }
}

# Copy essential files from the original installation if they don't exist in Config
$SourceRoot = ".\MirthConnect\CURR\Mirth Connect"
$FilesToCopy = @(
    "webapps\webadmin.war",
    "public_html\index.html",
    "public_api_html\index.html"
)

foreach ($file in $FilesToCopy) {
    $sourcePath = Join-Path $SourceRoot $file
    $destPath = Join-Path $ConfigRoot $file
    
    if ((Test-Path $sourcePath) -and !(Test-Path $destPath)) {
        $destDir = Split-Path $destPath -Parent
        if (!(Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
        Copy-Item -Path $sourcePath -Destination $destPath -Force
        Write-Host "Copied essential file: $file" -ForegroundColor Green
    }
}

Write-Host "`nMirthConnect Config directory setup completed!" -ForegroundColor Green
Write-Host "MirthConnect should now be able to run using configuration from: $ConfigRoot" -ForegroundColor Yellow
