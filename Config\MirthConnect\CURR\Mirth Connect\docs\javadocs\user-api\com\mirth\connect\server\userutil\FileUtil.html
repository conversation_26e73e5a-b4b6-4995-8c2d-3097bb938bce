<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:18 PDT 2023 -->
<title>FileUtil</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="FileUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/EncryptionUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/FileUtil.html" target="_top">Frames</a></li>
<li><a href="FileUtil.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.server.userutil</div>
<h2 title="Class FileUtil" class="title">Class FileUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.server.userutil.FileUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">FileUtil</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Provides file utility methods.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><code>FileUtils</code></dd>
</dl>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/FileUtil.html#decode-java.lang.String-">decode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;data)</code>
<div class="block">Decodes a Base64 string into octets.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/FileUtil.html#deleteFile-java.io.File-">deleteFile</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Deletes a specified File.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/FileUtil.html#encode-byte:A-">encode</a></span>(byte[]&nbsp;data)</code>
<div class="block">Encoded binary data into a Base64 string.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/FileUtil.html#read-java.lang.String-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Returns the contents of the file as a string, using the system default charset encoding.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/FileUtil.html#readBytes-java.lang.String-">readBytes</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Returns the contents of the file as a byte array.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/FileUtil.html#rtfToPlainText-java.lang.String-java.lang.String-">rtfToPlainText</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;replaceLinebreaksWith)</code>
<div class="block">Converts an RTF into plain text using the Swing RTFEditorKit.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/FileUtil.html#write-java.lang.String-boolean-byte:A-">write</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
     boolean&nbsp;append,
     byte[]&nbsp;bytes)</code>
<div class="block">Writes a byte array to a file, creating the file if it does not exist.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/FileUtil.html#write-java.lang.String-boolean-java.lang.String-">write</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
     boolean&nbsp;append,
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;data)</code>
<div class="block">Writes a string to a specified file, creating the file if it does not exist.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="write-java.lang.String-boolean-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public static&nbsp;void&nbsp;write(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
                         boolean&nbsp;append,
                         <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;data)
                  throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Writes a string to a specified file, creating the file if it does not exist.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileName</code> - The pathname string of the file to write to.</dd>
<dd><code>append</code> - If true, the data will be added to the end of the file rather than overwriting the
            file.</dd>
<dd><code>data</code> - The content to write to the file.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - If an I/O error occurred.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><code>FileUtils.writeStringToFile(File, String)</code></dd>
</dl>
</li>
</ul>
<a name="decode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>decode</h4>
<pre>public static&nbsp;byte[]&nbsp;decode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;data)</pre>
<div class="block">Decodes a Base64 string into octets.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - The Base64 string to decode.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The decoded data, as a byte array.s</dd>
</dl>
</li>
</ul>
<a name="encode-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>encode</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;encode(byte[]&nbsp;data)</pre>
<div class="block">Encoded binary data into a Base64 string.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - The binary data to encode (byte array).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The encoded Base64 string.</dd>
</dl>
</li>
</ul>
<a name="write-java.lang.String-boolean-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>write</h4>
<pre>public static&nbsp;void&nbsp;write(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName,
                         boolean&nbsp;append,
                         byte[]&nbsp;bytes)
                  throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Writes a byte array to a file, creating the file if it does not exist.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileName</code> - The pathname string of the file to write to.</dd>
<dd><code>append</code> - If true, the data will be added to the end of the file rather than overwriting the
            file.</dd>
<dd><code>bytes</code> - The binary content to write to the file.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - If an I/O error occurred.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><code>FileUtils.writeByteArrayToFile(File, byte[])</code></dd>
</dl>
</li>
</ul>
<a name="readBytes-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readBytes</h4>
<pre>public static&nbsp;byte[]&nbsp;readBytes(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)
                        throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Returns the contents of the file as a byte array.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileName</code> - The pathname string of the file to read from.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The byte array representation of the file.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - If an I/O error occurred.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><code>FileUtils.readFileToByteArray(File)</code></dd>
</dl>
</li>
</ul>
<a name="read-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;read(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)
                   throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Returns the contents of the file as a string, using the system default charset encoding.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileName</code> - The pathname string of the file to read from.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The string representation of the file.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - If an I/O error occurred.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><code>FileUtils.readFileToString(File)</code></dd>
</dl>
</li>
</ul>
<a name="deleteFile-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deleteFile</h4>
<pre>public static&nbsp;boolean&nbsp;deleteFile(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)
                          throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/SecurityException.html?is-external=true" title="class or interface in java.lang">SecurityException</a></pre>
<div class="block">Deletes a specified File. In Rhino and E4X 'delete' is a keyword, so File.delete() can't be
 called within Mirth directly.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - The File to delete.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if and only if the file or directory is successfully deleted; false otherwise</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/SecurityException.html?is-external=true" title="class or interface in java.lang">SecurityException</a></code> - If the security manager denies access to delete the file.</dd>
</dl>
</li>
</ul>
<a name="rtfToPlainText-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>rtfToPlainText</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;rtfToPlainText(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
                                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;replaceLinebreaksWith)
                             throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a>,
                                    <a href="https://docs.oracle.com/javase/8/docs/api/javax/swing/text/BadLocationException.html?is-external=true" title="class or interface in javax.swing.text">BadLocationException</a></pre>
<div class="block">Converts an RTF into plain text using the Swing RTFEditorKit.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - The RTF message to convert.</dd>
<dd><code>replaceLinebreaksWith</code> - If not null, any line breaks in the converted message will be replaced with this
            string.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted plain text message.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - If an I/O error occurred.</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/javax/swing/text/BadLocationException.html?is-external=true" title="class or interface in javax.swing.text">BadLocationException</a></code> - If an invalid location within the document is used.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/EncryptionUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/FileUtil.html" target="_top">Frames</a></li>
<li><a href="FileUtil.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
