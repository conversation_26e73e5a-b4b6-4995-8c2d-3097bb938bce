<connectorMetaData path="file">
	<name>File Writer</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to write files to a local or network file system. A velocity template is available to dynamically generate files.</description>
	<clientClassName>com.mirth.connect.connectors.file.FileWriter</clientClassName>
	<serverClassName>com.mirth.connect.connectors.file.FileDispatcher</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.file.FileDispatcherProperties</sharedClassName>
	<library type="CLIENT" path="file-client.jar" />
	<library type="SHARED" path="file-shared.jar" />
	<library type="SERVER" path="file-server.jar" />
	<library type="SERVER" path="lib/jcifs-ng-2.1.8.jar" />
	<library type="SERVER" path="lib/webdavclient4j-core-0.92.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.connectors.file.FileConnectorServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.connectors.file.FileConnectorServlet"/>
	<protocol>file</protocol>
	<type>DESTINATION</type>
</connectorMetaData>
