# MirthConnect Configuration Setup Guide

## Overview

This guide explains how to configure MirthConnect to load its configuration from the centralized Config folder instead of the default installation directory.

## Changes Made

### 1. Modified VM Options Files

**Files Modified:**
- `MirthConnect\CURR\Mirth Connect\mcservice.vmoptions`
- `MirthConnect\CURR\Mirth Connect\mcserver.vmoptions`

**Change Applied:**
Added the following line to both files:
```
-Duser.dir=C:\Mobile Aspects\Config\MirthConnect\CURR\Mirth Connect
```

This system property tells the Java Virtual Machine to use the Config folder as the working directory, which makes MirthConnect look for its configuration files in that location.

### 2. Configuration Directory Structure

The Config folder now contains:
```
Config\MirthConnect\CURR\Mirth Connect\
├── conf\                    # Configuration files
│   ├── mirth.properties     # Main configuration
│   ├── dbdrivers.xml       # Database drivers
│   ├── log4j2.properties   # Logging configuration
│   └── ...
├── extensions\              # MirthConnect extensions
├── appdata\                 # Application data
├── logs\                    # Log files (created automatically)
├── webapps\                 # Web applications
├── public_html\             # Public web content
└── public_api_html\         # API documentation
```

## Setup Instructions

### Step 1: Run the Configuration Copy Script

First, ensure all configuration files are copied to the Config folder:

```powershell
.\Script\Util_CopyConfigFiles\copy-config-files.ps1
```

### Step 2: Set Up Required Directories

Run the directory setup script to create necessary folders:

```powershell
.\Script\Util_CopyConfigFiles\setup-mirth-config-directories.ps1
```

### Step 3: Stop MirthConnect Service

Before applying changes, stop the MirthConnect service:

```powershell
Stop-Service -Name "MA_MirthConnect"
```

### Step 4: Verify Configuration

Check that the Config folder contains all necessary files:
- Configuration files in `Config\MirthConnect\CURR\Mirth Connect\conf\`
- Extensions in `Config\MirthConnect\CURR\Mirth Connect\extensions\`
- Application data in `Config\MirthConnect\CURR\Mirth Connect\appdata\`

### Step 5: Start MirthConnect Service

Start the service to test the new configuration:

```powershell
Start-Service -Name "MA_MirthConnect"
```

## Verification

### Check Service Status
```powershell
Get-Service -Name "MA_MirthConnect"
```

### Check Log Files
Monitor the log files in the Config folder:
```
Config\MirthConnect\CURR\Mirth Connect\logs\mirth.log
```

### Access MirthConnect Administrator
The MirthConnect Administrator should connect to the same instance, now running from the Config folder.

## Benefits

1. **Centralized Configuration**: All configuration files are now in the Config folder
2. **Version Control**: Configuration changes can be tracked in the Config folder
3. **Backup and Restore**: Easier to backup and restore configurations
4. **Environment Management**: Different environments can have separate Config folders
5. **Deployment**: Configuration changes can be deployed independently of the application

## Troubleshooting

### Service Won't Start

1. **Check VM Options**: Verify the `-Duser.dir` parameter is correctly set in both `.vmoptions` files
2. **Check Permissions**: Ensure the service account has read/write access to the Config folder
3. **Check Paths**: Verify all paths in `mirth.properties` are accessible from the new working directory

### Configuration Not Loading

1. **Verify Working Directory**: Check that MirthConnect is actually using the Config folder as working directory
2. **Check File Paths**: Ensure all relative paths in configuration files are correct
3. **Check Extensions**: Verify that extensions are properly loaded from the Config folder

### Database Connection Issues

1. **Check Database Configuration**: Verify database settings in `Config\MirthConnect\CURR\Mirth Connect\conf\mirth.properties`
2. **Check Data Directory**: Ensure `dir.appdata` points to the correct location
3. **Check Permissions**: Verify database access permissions

## Rollback Procedure

If you need to revert to the original configuration:

1. Stop the MirthConnect service
2. Edit both `.vmoptions` files and remove the `-Duser.dir` line
3. Start the MirthConnect service

The service will then use the original installation directory for configuration.

## File Locations

### Original Installation
```
C:\Mobile Aspects\MirthConnect\CURR\Mirth Connect\
```

### New Configuration Location
```
C:\Mobile Aspects\Config\MirthConnect\CURR\Mirth Connect\
```

### Modified Files
- `MirthConnect\CURR\Mirth Connect\mcservice.vmoptions`
- `MirthConnect\CURR\Mirth Connect\mcserver.vmoptions`

## Notes

- The executable files (mcservice.exe, mcserver.exe, etc.) remain in the original installation directory
- Only configuration files, extensions, and data are loaded from the Config folder
- This approach maintains the original installation while centralizing configuration management
- The change affects both service and standalone execution modes
