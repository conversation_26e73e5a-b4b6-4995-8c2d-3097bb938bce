<pluginMetaData path="datatype-edi">
	<name>EDI Data Type</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This plugin provides support for the EDI data type</description>
	<serverClasses>
		<string>com.mirth.connect.plugins.datatypes.edi.EDIDataTypeServerPlugin</string>
	</serverClasses>
	<clientClasses>
		<string>com.mirth.connect.plugins.datatypes.edi.EDIDataTypeClientPlugin</string>
	</clientClasses>
	<templateClassName>com.mirth.connect.plugins.datatypes.edi.EDIDataTypeCodeTemplatePlugin</templateClassName>
	<library type="CLIENT" path="datatype-edi-client.jar" />
	<library type="SHARED" path="datatype-edi-shared.jar" />
	<library type="SERVER" path="datatype-edi-server.jar" />
</pluginMetaData>
