# MirthConnect Service Creation Script

## Overview

This PowerShell script automatically creates a Windows service for MirthConnect with the parent folder name included in the service name. This allows multiple versions of MirthConnect to run as separate services.

## How It Works

The script automatically:
1. **Detects Parent Folder**: Gets the parent folder name (e.g., "C<PERSON><PERSON>", "4.4.2", etc.)
2. **Creates Service Name**: Combines "MA_MirthConnect_" with the parent folder name
3. **Sets Display Name**: Creates a user-friendly display name
4. **Locates Executable**: Uses relative path `..\..\mcservice.exe` to find the executable
5. **Creates Service**: Registers the Windows service with proper configuration

## Example

For the folder structure:
```
MirthConnect/
├── CURR/
│   ├── Scripts/Mirth_CreateService/Mirth_CreateService.ps1
│   └── mcservice.exe
└── 4.4.2/
    ├── Scripts/Mirth_CreateService/Mirth_CreateService.ps1
    └── mcservice.exe
```

Running the script from `CURR` folder creates:
- **Service Name**: `MA_MirthConnect_CURR`
- **Display Name**: `MA MirthConnect (CURR)`
- **Executable**: `C:\Mobile Aspects\MirthConnect\CURR\mcservice.exe`

## Usage

### Prerequisites
- Run PowerShell as Administrator
- Ensure `mcservice.exe` exists in the parent directory

### Running the Script
```powershell
# Navigate to the script directory
cd "C:\Mobile Aspects\MirthConnect\CURR\Scripts\Mirth_CreateService"

# Run the script
.\Mirth_CreateService.ps1
```

### Interactive Options
The script will:
1. Display the service details it will create
2. Create the Windows service
3. Ask if you want to start the service immediately

## Service Details

| Property | Value |
|----------|-------|
| Service Name | `MA_MirthConnect_{ParentFolder}` |
| Display Name | `MA MirthConnect ({ParentFolder})` |
| Startup Type | Automatic (Delayed Start) |
| Executable | `{ParentFolder}\mcservice.exe` |

## Benefits

1. **Multiple Versions**: Run different MirthConnect versions simultaneously
2. **Clear Identification**: Service names clearly indicate which version
3. **Automatic Detection**: No manual configuration needed
4. **Consistent Naming**: Standardized naming convention across versions
5. **Easy Management**: Each version has its own service for independent control

## Troubleshooting

### Common Issues

**"Access Denied" Error**
- Solution: Run PowerShell as Administrator

**"Service already exists" Error**
- Solution: Delete existing service first:
  ```powershell
  sc delete MA_MirthConnect_CURR
  ```

**"File not found" Error**
- Solution: Ensure `mcservice.exe` exists in the parent directory

**Service won't start**
- Check that MirthConnect configuration files are present
- Verify database connectivity
- Review Windows Event Logs for detailed error messages

### Verification

After running the script, verify the service was created:
```powershell
Get-Service -Name "MA_MirthConnect_*"
```

Check service details:
```powershell
Get-WmiObject -Class Win32_Service | Where-Object {$_.Name -like "MA_MirthConnect_*"} | Select-Object Name, DisplayName, PathName, State
```

## Related Files

- `mcservice.exe` - MirthConnect Windows service executable
- `mcservice.vmoptions` - JVM options for service mode
- `conf/mirth.properties` - MirthConnect configuration

## Notes

- The script uses the folder structure to automatically determine the service name
- Each MirthConnect version should have its own copy of this script
- Services created with this script will use the configuration from their respective folders
- The script includes error handling and user feedback for better reliability
