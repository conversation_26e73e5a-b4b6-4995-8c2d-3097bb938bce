<connectorMetaData path="smtp">
	<name>SMTP Sender</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to send email messages via SMTP. A template is available to dynamically create messages.</description>
	<clientClassName>com.mirth.connect.connectors.smtp.SmtpSender</clientClassName>
	<serverClassName>com.mirth.connect.connectors.smtp.SmtpDispatcher</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.smtp.SmtpDispatcherProperties</sharedClassName>
	<library type="CLIENT" path="smtp-client.jar" />
	<library type="SHARED" path="smtp-shared.jar" />
	<library type="SERVER" path="smtp-server.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.connectors.smtp.SmtpConnectorServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.connectors.smtp.SmtpConnectorServlet"/>
	<transformers></transformers>
	<protocol>smtp</protocol>
	<type>DESTINATION</type>
</connectorMetaData>
