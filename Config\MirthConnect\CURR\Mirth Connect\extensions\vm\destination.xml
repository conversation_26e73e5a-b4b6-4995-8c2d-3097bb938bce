<connectorMetaData path="vm">
	<name>Channel Writer</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to send events to other channels in the same Mirth Connect instance.</description>
	<clientClassName>com.mirth.connect.connectors.vm.ChannelWriter</clientClassName>
	<serverClassName>com.mirth.connect.connectors.vm.VmDispatcher</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.vm.VmDispatcherProperties</sharedClassName>
	<library type="CLIENT" path="vm-client.jar" />
	<library type="SHARED" path="vm-shared.jar" />
	<library type="SERVER" path="vm-server.jar" />
	<transformers></transformers>
	<protocol>vm</protocol>
	<type>DESTINATION</type>
</connectorMetaData>
