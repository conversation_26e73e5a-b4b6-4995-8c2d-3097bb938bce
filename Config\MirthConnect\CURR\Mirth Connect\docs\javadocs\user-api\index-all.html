<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:21 PDT 2023 -->
<title>Index</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<script type="text/javascript" src="script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Index";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?index-all.html" target="_top">Frames</a></li>
<li><a href="index-all.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="contentContainer"><a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:J">J</a>&nbsp;<a href="#I:K">K</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a href="#I:W">W</a>&nbsp;<a href="#I:X">X</a>&nbsp;<a name="I:A">
<!--   -->
</a>
<h2 class="title">A</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#absolute-int-">absolute(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#acceptChanges--">acceptChanges()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#acceptChanges-java.sql.Connection-">acceptChanges(Connection)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/mirth/connect/server/userutil/ACKGenerator.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">ACKGenerator</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Allows users to generate HL7 v2.x acknowledgments based on an inbound message, with a specified
 ACK code and custom text message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ACKGenerator.html#ACKGenerator--">ACKGenerator()</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ACKGenerator.html" title="class in com.mirth.connect.server.userutil">ACKGenerator</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span></div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#add-java.lang.Object-">add(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Appends the specified element to the end of this list (optional
 operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#add-int-java.lang.Object-">add(int, Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Inserts the specified element at the specified position in this list
 (optional operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#add-java.lang.Object-java.lang.Object-">add(Object, Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Adds an entry to the map using the <a href="com/mirth/connect/userutil/MapBuilder.html#put-java.lang.Object-java.lang.Object-"><code>MapBuilder.put(java.lang.Object, java.lang.Object)</code></a> method, and returns this builder.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#addAll-java.util.Collection-">addAll(Collection)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Appends all of the elements in the specified collection to the end of
 this list, in the order that they are returned by the specified
 collection's iterator (optional operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#addAll-int-java.util.Collection-">addAll(int, Collection)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Inserts all of the elements in the specified collection into this
 list at the specified position (optional operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#addAttachment-java.util.List-java.lang.Object-java.lang.String-">addAttachment(List&lt;Attachment&gt;, Object, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Creates an Attachment and adds it to the provided list.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#addAttachment-java.util.List-java.lang.Object-java.lang.String-boolean-">addAttachment(List&lt;Attachment&gt;, Object, String, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Creates an Attachment and adds it to the provided list.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#addResponseHeader-java.lang.String-java.lang.String-">addResponseHeader(String, String)</a></span> - Method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Adds a new response header to be sent along with the authentication response.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#addRowSetListener-javax.sql.RowSetListener-">addRowSetListener(RowSetListener)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#afterLast--">afterLast()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/mirth/connect/server/userutil/AlertSender.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">AlertSender</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Allows users to dispatch error events which can be alerted on.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AlertSender.html#AlertSender-java.lang.String-">AlertSender(String)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AlertSender.html" title="class in com.mirth.connect.server.userutil">AlertSender</a></dt>
<dd>
<div class="block">Instantiates a new AlertSender.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AlertSender.html#AlertSender-com.mirth.connect.userutil.ImmutableConnectorMessage-">AlertSender(ImmutableConnectorMessage)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AlertSender.html" title="class in com.mirth.connect.server.userutil">AlertSender</a></dt>
<dd>
<div class="block">Instantiates a new AlertSender.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#append-java.lang.Object-">append(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Adds an element to the list using the <a href="com/mirth/connect/userutil/ListBuilder.html#add-java.lang.Object-"><code>ListBuilder.add(java.lang.Object)</code></a> method, and returns this builder.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Attachment</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Used to store and retrieve details about message attachments such as the ID, MIME type, and
 content.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#Attachment--">Attachment()</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Instantiates a new Attachment with no ID, content, or MIME type.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#Attachment-java.lang.String-byte:A-java.lang.String-">Attachment(String, byte[], String)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Instantiates a new Attachment.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#Attachment-java.lang.String-java.lang.String-java.lang.String-">Attachment(String, String, String)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Instantiates a new Attachment with String data using UTF-8 charset encoding.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#Attachment-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">Attachment(String, String, String, String)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Instantiates a new Attachment with String data and a given charset encoding.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/AttachmentEntry.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">AttachmentEntry</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">Used to store and retrieve details about message attachments such as the name, contents, and
 MIME type.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/AttachmentEntry.html#AttachmentEntry--">AttachmentEntry()</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/AttachmentEntry.html" title="class in com.mirth.connect.userutil">AttachmentEntry</a></dt>
<dd>
<div class="block">Instantiates a new AttachmentEntry with no name, content, or MIME type.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/AttachmentEntry.html#AttachmentEntry-com.mirth.connect.userutil.AttachmentEntry-">AttachmentEntry(AttachmentEntry)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/AttachmentEntry.html" title="class in com.mirth.connect.userutil">AttachmentEntry</a></dt>
<dd>
<div class="block">Instantiates a new AttachmentEntry that copies the name, content, and MIME type
 from a given AttachmentEntry object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/AttachmentEntry.html#AttachmentEntry-java.lang.String-java.lang.String-java.lang.String-">AttachmentEntry(String, String, String)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/AttachmentEntry.html" title="class in com.mirth.connect.userutil">AttachmentEntry</a></dt>
<dd>
<div class="block">Instantiates a new AttachmentEntry with a name, content, and a MIME type.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">AttachmentUtil</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Provides utility methods for creating, retrieving, and re-attaching message attachments.</div>
</dd>
<dt><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil"><span class="typeNameLink">AuthenticationResult</span></a> - Class in <a href="com/mirth/connect/plugins/httpauth/userutil/package-summary.html">com.mirth.connect.plugins.httpauth.userutil</a></dt>
<dd>
<div class="block">This class represents the result of an HTTP authentication attempt, used to accept or reject
 requests coming into HTTP-based source connectors.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#AuthenticationResult-com.mirth.connect.plugins.httpauth.userutil.AuthStatus-">AuthenticationResult(AuthStatus)</a></span> - Constructor for class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Instantiates a new AuthenticationResult object.</div>
</dd>
<dt><a href="com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html" title="enum in com.mirth.connect.plugins.httpauth.userutil"><span class="typeNameLink">AuthStatus</span></a> - Enum in <a href="com/mirth/connect/plugins/httpauth/userutil/package-summary.html">com.mirth.connect.plugins.httpauth.userutil</a></dt>
<dd>
<div class="block">Denotes the result of an HTTP authentication attempt.</div>
</dd>
</dl>
<a name="I:B">
<!--   -->
</a>
<h2 class="title">B</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#beforeFirst--">beforeFirst()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#byteArrayToDicomObject-byte:A-boolean-">byteArrayToDicomObject(byte[], boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Converts a byte array into a dcm4che DicomObject.</div>
</dd>
</dl>
<a name="I:C">
<!--   -->
</a>
<h2 class="title">C</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Future.html#cancel-boolean-">cancel(boolean)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a></dt>
<dd>
<div class="block">Attempts to cancel execution of this task.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#cancelRowUpdates--">cancelRowUpdates()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#Challenged-java.lang.String-">Challenged(String)</a></span> - Static method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Convenience method to create a new AuthenticationResult with the CHALLENGED status.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">ChannelMap</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">A wrapper class for the channel map that checks against the source map in the <a href="com/mirth/connect/server/userutil/ChannelMap.html#get-java.lang.Object-"><code>get(key)</code></a> method for legacy support.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#ChannelMap-java.util.Map-java.util.Map-">ChannelMap(Map&lt;String, Object&gt;, Map&lt;String, Object&gt;)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>
<div class="block">Instantiates a new ChannelMap object.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">ChannelUtil</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">This utility class allows the user to query information from channels or to perform actions on
 channels.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#clear--">clear()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#clear--">clear()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#clear--">clear()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Removes all of the elements from this list (optional operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#clear--">clear()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Removes all of the mappings from this map (optional operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#clear--">clear()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#clearMessage--">clearMessage()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Removes references to any data (textual or binary) currently stored by the raw message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#clearParameters--">clearParameters()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#clearWarnings--">clearWarnings()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#close--">close()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Closes the database connection.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#close--">close()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#columnUpdated-java.lang.String-">columnUpdated(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#columnUpdated-int-">columnUpdated(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/mirth/connect/plugins/httpauth/userutil/package-summary.html">com.mirth.connect.plugins.httpauth.userutil</a> - package com.mirth.connect.plugins.httpauth.userutil</dt>
<dd>
<div class="block">This package is included in the JavaScript scope on the server.&nbsp;Classes in this package are
 part of the supported User API for use in channels/scripts.&nbsp;Reference to any class in Mirth
 Connect outside of the userutil packages is unsupported.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a> - package com.mirth.connect.server.userutil</dt>
<dd>
<div class="block">This package is included in the JavaScript scope on the server.&nbsp;Classes in this package are
 part of the supported User API for use in channels/scripts.&nbsp;Reference to any class in Mirth
 Connect outside of the userutil packages is unsupported.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a> - package com.mirth.connect.userutil</dt>
<dd>
<div class="block">This package is included in the JavaScript scope on both the client and the server.&nbsp;Classes
 in this package are part of the supported User API for use in channels/scripts.&nbsp;Reference to
 any class in Mirth Connect outside of the userutil packages is unsupported.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#commit--">commit()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Makes all changes made since the previous commit/rollback permanent and releases any database
 locks currently held by this DatabaseConnection object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#commit--">commit()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#contains-java.lang.Object-">contains(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns <tt>true</tt> if this list contains the specified element.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageHeaders.html#contains-java.lang.String-">contains(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageHeaders.html" title="class in com.mirth.connect.userutil">MessageHeaders</a></dt>
<dd>
<div class="block">Check if headers exists for a given key.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageParameters.html#contains-java.lang.String-">contains(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageParameters.html" title="class in com.mirth.connect.userutil">MessageParameters</a></dt>
<dd>
<div class="block">Check if parameters exist for a given key.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#containsAll-java.util.Collection-">containsAll(Collection)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns <tt>true</tt> if this list contains all of the elements of the
 specified collection.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#containsKey-java.lang.Object-">containsKey(Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#containsKey-java.lang.Object-">containsKey(Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#containsKey-java.lang.Object-">containsKey(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Returns <tt>true</tt> if this map contains a mapping for the specified
 key.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#containsKey-java.lang.Object-">containsKey(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#containsValue-java.lang.Object-">containsValue(Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#containsValue-java.lang.Object-">containsValue(Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#containsValue-java.lang.Object-">containsValue(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Returns <tt>true</tt> if this map maps one or more keys to the
 specified value.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#containsValue-java.lang.Object-">containsValue(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/mirth/connect/userutil/ContentType.html" title="enum in com.mirth.connect.userutil"><span class="typeNameLink">ContentType</span></a> - Enum in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">Denotes various types of content created by a channel.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/ContextFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">ContextFactory</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Allows the user to retrieve information about the current JavaScript context.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ContextFactory.html#ContextFactory-com.mirth.connect.server.util.javascript.MirthContextFactory-">ContextFactory(MirthContextFactory)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ContextFactory.html" title="class in com.mirth.connect.server.userutil">ContextFactory</a></dt>
<dd>
<div class="block">Instantiates a new ContextFactory object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DateUtil.html#convertDate-java.lang.String-java.lang.String-java.lang.String-">convertDate(String, String, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DateUtil.html" title="class in com.mirth.connect.server.userutil">DateUtil</a></dt>
<dd>
<div class="block">Parses a date string according to a specified input pattern, and formats the date back to a
 string according to a specified output pattern.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOM-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-boolean-">convertDICOM(String, ImmutableConnectorMessage, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOM-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-">convertDICOM(String, ImmutableConnectorMessage)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOM-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-int-">convertDICOM(String, ImmutableConnectorMessage, int)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOM-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-int-boolean-">convertDICOM(String, ImmutableConnectorMessage, int, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOMToByteArray-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-">convertDICOMToByteArray(String, ImmutableConnectorMessage)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOMToByteArray-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-int-">convertDICOMToByteArray(String, ImmutableConnectorMessage, int)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOMToByteArray-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-int-boolean-">convertDICOMToByteArray(String, ImmutableConnectorMessage, int, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#createAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.Object-java.lang.String-">createAttachment(ImmutableConnectorMessage, Object, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Creates an attachment associated with a given connector message, and inserts it into the
 database.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#createAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.Object-java.lang.String-boolean-">createAttachment(ImmutableConnectorMessage, Object, String, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Creates an attachment associated with a given connector message, and inserts it into the
 database.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnectionFactory.html#createConnection-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">createConnection(String, String, String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnectionFactory.html" title="class in com.mirth.connect.server.userutil">DatabaseConnectionFactory</a></dt>
<dd>
<div class="block">Instantiates and returns a new java.sql.Connection object with the given connection
 parameters.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#createCopy--">createCopy()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#createCopyNoConstraints--">createCopyNoConstraints()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#createCopySchema--">createCopySchema()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnectionFactory.html#createDatabaseConnection-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">createDatabaseConnection(String, String, String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnectionFactory.html" title="class in com.mirth.connect.server.userutil">DatabaseConnectionFactory</a></dt>
<dd>
<div class="block">Instantiates and returns a new DatabaseConnection object with the given connection
 parameters.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnectionFactory.html#createDatabaseConnection-java.lang.String-java.lang.String-">createDatabaseConnection(String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnectionFactory.html" title="class in com.mirth.connect.server.userutil">DatabaseConnectionFactory</a></dt>
<dd>
<div class="block">Instantiates and returns a new DatabaseConnection object with the given connection
 parameters.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#createShared--">createShared()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnectionFactory.html#createSMTPConnection--">createSMTPConnection()</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnectionFactory.html" title="class in com.mirth.connect.server.userutil">SMTPConnectionFactory</a></dt>
<dd>
<div class="block">Creates an create SMTPConnection object using the server's default SMTP settings.</div>
</dd>
</dl>
<a name="I:D">
<!--   -->
</a>
<h2 class="title">D</h2>
<dl>
<dt><a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">DatabaseConnection</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Provides the ability to run SQL queries again the database connection object instantiated using
 DatabaseConnectionFactory.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#DatabaseConnection-java.lang.String-">DatabaseConnection(String)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Instantiates a new database connection with the given server address.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#DatabaseConnection-java.lang.String-java.util.Properties-">DatabaseConnection(String, Properties)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Instantiates a new database connection with the given server address and connection
 arguments.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#DatabaseConnection-java.sql.Driver-java.lang.String-">DatabaseConnection(Driver, String)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Instantiates a new database connection with the given driver instance and server address.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#DatabaseConnection-java.sql.Driver-java.lang.String-java.util.Properties-">DatabaseConnection(Driver, String, Properties)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Instantiates a new database connection with the given driver instance, server address, and
 connection arguments.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/DatabaseConnectionFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">DatabaseConnectionFactory</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Used to create database connection objects.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnectionFactory.html#DatabaseConnectionFactory-com.mirth.connect.server.util.javascript.MirthContextFactory-">DatabaseConnectionFactory(MirthContextFactory)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnectionFactory.html" title="class in com.mirth.connect.server.userutil">DatabaseConnectionFactory</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/mirth/connect/server/userutil/DateUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">DateUtil</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Provides date/time utility methods.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/FileUtil.html#decode-java.lang.String-">decode(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/FileUtil.html" title="class in com.mirth.connect.server.userutil">FileUtil</a></dt>
<dd>
<div class="block">Decodes a Base64 string into octets.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/XmlUtil.html#decode-java.lang.String-">decode(String)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/XmlUtil.html" title="class in com.mirth.connect.userutil">XmlUtil</a></dt>
<dd>
<div class="block">Converts an XML/HTML entity reference into a string with the literal character.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/EncryptionUtil.html#decrypt-java.lang.String-">decrypt(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/EncryptionUtil.html" title="class in com.mirth.connect.server.userutil">EncryptionUtil</a></dt>
<dd>
<div class="block">Convenience method for decrypting data.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/EncryptionUtil.html#decrypt-java.lang.String-byte:A-">decrypt(String, byte[])</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/EncryptionUtil.html" title="class in com.mirth.connect.server.userutil">EncryptionUtil</a></dt>
<dd>
<div class="block">Convenience method for decrypting data.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/FileUtil.html#deleteFile-java.io.File-">deleteFile(File)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/FileUtil.html" title="class in com.mirth.connect.server.userutil">FileUtil</a></dt>
<dd>
<div class="block">Deletes a specified File.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#deleteRow--">deleteRow()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#deployChannel-java.lang.String-">deployChannel(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Deploy a channel.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/DeployedState.html" title="enum in com.mirth.connect.server.userutil"><span class="typeNameLink">DeployedState</span></a> - Enum in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">States of UNDEPLOYED, DEPLOYING, UNDEPLOYING, STARTING, STARTED, PAUSING, PAUSED, STOPPING,
 STOPPED</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/DestinationSet.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">DestinationSet</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Utility class used in the preprocessor or source filter/transformer to prevent the message from
 being sent to specific destinations.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DestinationSet.html#DestinationSet-com.mirth.connect.userutil.ImmutableConnectorMessage-">DestinationSet(ImmutableConnectorMessage)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DestinationSet.html" title="class in com.mirth.connect.server.userutil">DestinationSet</a></dt>
<dd>
<div class="block">DestinationSet instances should NOT be constructed manually.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#dicomObjectToByteArray-org.dcm4che2.data.DicomObject-">dicomObjectToByteArray(DicomObject)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Converts a dcm4che DicomObject into a byte array.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">DICOMUtil</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Provides DICOM utility methods.</div>
</dd>
</dl>
<a name="I:E">
<!--   -->
</a>
<h2 class="title">E</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/FileUtil.html#encode-byte:A-">encode(byte[])</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/FileUtil.html" title="class in com.mirth.connect.server.userutil">FileUtil</a></dt>
<dd>
<div class="block">Encoded binary data into a Base64 string.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/XmlUtil.html#encode-char-">encode(char)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/XmlUtil.html" title="class in com.mirth.connect.userutil">XmlUtil</a></dt>
<dd>
<div class="block">Encodes a character into the corresponding XML/HTML entity.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/XmlUtil.html#encode-java.lang.String-">encode(String)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/XmlUtil.html" title="class in com.mirth.connect.userutil">XmlUtil</a></dt>
<dd>
<div class="block">Converts a string, encoding characters into the corresponding XML/HTML entities as needed.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/XmlUtil.html#encode-char:A-int-int-">encode(char[], int, int)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/XmlUtil.html" title="class in com.mirth.connect.userutil">XmlUtil</a></dt>
<dd>
<div class="block">Converts a character array, encoding characters into the corresponding XML/HTML entities as
 needed.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/EncryptionUtil.html#encrypt-java.lang.String-">encrypt(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/EncryptionUtil.html" title="class in com.mirth.connect.server.userutil">EncryptionUtil</a></dt>
<dd>
<div class="block">Convenience method for encrypting data.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/EncryptionUtil.html#encrypt-byte:A-">encrypt(byte[])</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/EncryptionUtil.html" title="class in com.mirth.connect.server.userutil">EncryptionUtil</a></dt>
<dd>
<div class="block">Convenience method for encrypting data.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/EncryptedData.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">EncryptedData</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">This object is returned from <a href="com/mirth/connect/server/userutil/EncryptionUtil.html#encrypt-byte:A-"><code>EncryptionUtil.encrypt(byte[])</code></a>.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/EncryptedData.html#EncryptedData-java.lang.String-byte:A-">EncryptedData(String, byte[])</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/EncryptedData.html" title="class in com.mirth.connect.server.userutil">EncryptedData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/mirth/connect/server/userutil/EncryptionUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">EncryptionUtil</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">This utility class provides some convenience methods for encrypting or decrypting data.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/EncryptionUtil.html#EncryptionUtil--">EncryptionUtil()</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/EncryptionUtil.html" title="class in com.mirth.connect.server.userutil">EncryptionUtil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#entrySet--">entrySet()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#entrySet--">entrySet()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#entrySet--">entrySet()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Returns a <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util"><code>Set</code></a> view of the mappings contained in this map.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#entrySet--">entrySet()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#equals-java.lang.Object-">equals(Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#equals-java.lang.Object-">equals(Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/AttachmentEntry.html#equals-java.lang.Object-">equals(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/AttachmentEntry.html" title="class in com.mirth.connect.userutil">AttachmentEntry</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#equals-java.lang.Object-">equals(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Indicates whether some other object is "equal to" this one.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#equals-java.lang.Object-">equals(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Indicates whether some other object is "equal to" this one.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#equals-java.lang.Object-">equals(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Indicates whether some other object is "equal to" this one.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#equals-java.lang.Object-">equals(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/JsonUtil.html#escape-java.lang.String-">escape(String)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/JsonUtil.html" title="class in com.mirth.connect.userutil">JsonUtil</a></dt>
<dd>
<div class="block">Escapes any special JSON characters in the input.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#execute--">execute()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#execute-java.sql.Connection-">execute(Connection)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#executeCachedQuery-java.lang.String-">executeCachedQuery(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Executes a query on the database and returns a CachedRowSet.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#executeCachedQuery-java.lang.String-java.util.List-">executeCachedQuery(String, List&lt;Object&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Executes a prepared query on the database and returns a CachedRowSet.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#executeUpdate-java.lang.String-">executeUpdate(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Executes an INSERT/UPDATE on the database and returns the row count.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#executeUpdate-java.lang.String-java.util.List-">executeUpdate(String, List&lt;Object&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Executes a prepared INSERT/UPDATE statement on the database and returns the row count.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#executeUpdateAndGetGeneratedKeys-java.lang.String-">executeUpdateAndGetGeneratedKeys(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Executes an INSERT/UPDATE statement on the database and returns a CachedRowSet containing any
 generated keys.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#executeUpdateAndGetGeneratedKeys-java.lang.String-java.util.List-">executeUpdateAndGetGeneratedKeys(String, List&lt;Object&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Executes a prepared INSERT/UPDATE statement on the database and returns a CachedRowSet
 containing any generated keys.</div>
</dd>
</dl>
<a name="I:F">
<!--   -->
</a>
<h2 class="title">F</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#Failure--">Failure()</a></span> - Static method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Convenience method to create a new AuthenticationResult with the FAILURE status.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/FileUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">FileUtil</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Provides file utility methods.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#findColumn-java.lang.String-">findColumn(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#first--">first()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DateUtil.html#formatDate-java.lang.String-java.util.Date-">formatDate(String, Date)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DateUtil.html" title="class in com.mirth.connect.server.userutil">DateUtil</a></dt>
<dd>
<div class="block">Formats a java.util.Date object into a string according to a specified pattern.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/NCPDPUtil.html#formatNCPDPNumber-java.lang.String-int-">formatNCPDPNumber(String, int)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/NCPDPUtil.html" title="class in com.mirth.connect.server.userutil">NCPDPUtil</a></dt>
<dd>
<div class="block">Converts a signed overpunch code into a string representing the appropriate decimal value.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Future</span></a>&lt;<a href="com/mirth/connect/server/userutil/Future.html" title="type parameter in Future">V</a>&gt; - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">A <code>Future</code> represents the result of an asynchronous computation.</div>
</dd>
</dl>
<a name="I:G">
<!--   -->
</a>
<h2 class="title">G</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/HashUtil.html#generate-java.lang.Object-">generate(Object)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/HashUtil.html" title="class in com.mirth.connect.server.userutil">HashUtil</a></dt>
<dd>
<div class="block">Takes in any object and generates a SHA-256 hex hash.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/HashUtil.html#generate-java.lang.String-java.lang.String-java.lang.String-">generate(String, String, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/HashUtil.html" title="class in com.mirth.connect.server.userutil">HashUtil</a></dt>
<dd>
<div class="block">Takes in a string, an encoding, and a hashing algorithm and generates a hex hash.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/HashUtil.html#generate-byte:A-java.lang.String-">generate(byte[], String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/HashUtil.html" title="class in com.mirth.connect.server.userutil">HashUtil</a></dt>
<dd>
<div class="block">Takes in a byte[], an encoding, and a hashing algorithm and generates a hex hash.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ACKGenerator.html#generateAckResponse-java.lang.String-java.lang.String-java.lang.String-">generateAckResponse(String, String, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ACKGenerator.html" title="class in com.mirth.connect.server.userutil">ACKGenerator</a></dt>
<dd>
<div class="block">Generates an HL7 v2.x acknowledgment.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ACKGenerator.html#generateAckResponse-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">generateAckResponse(String, String, String, String, String, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ACKGenerator.html" title="class in com.mirth.connect.server.userutil">ACKGenerator</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             generateAckResponse(message, isXML, acknowledgementCode, textMessage, dateFormat,
             errorMessage) instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ACKGenerator.html#generateAckResponse-java.lang.String-boolean-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">generateAckResponse(String, boolean, String, String, String, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ACKGenerator.html" title="class in com.mirth.connect.server.userutil">ACKGenerator</a></dt>
<dd>
<div class="block">Generates an HL7 v2.x acknowledgment.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#get-java.lang.Object-">get(Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>
<div class="block">Returns the value to which the specified key is mapped, or null if this map contains no
 mapping for the key.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Future.html#get--">get()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a></dt>
<dd>
<div class="block">Waits if necessary for the computation to complete, and then retrieves its result.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Future.html#get-long-">get(long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a></dt>
<dd>
<div class="block">Waits if necessary for at most the given time for the computation to complete, and then
 retrieves its result, if available.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Future.html#get-long-java.util.concurrent.TimeUnit-">get(long, TimeUnit)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a></dt>
<dd>
<div class="block">Waits if necessary for at most the given time for the computation to complete, and then
 retrieves its result, if available.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#get-java.lang.Object-">get(Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#get-int-">get(int)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns the element at the specified position in this list.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#get-java.lang.Object-">get(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Returns the value to which the specified key is mapped,
 or <code>null</code> if this map contains no mapping for the key.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageHeaders.html#get-java.lang.String-">get(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageHeaders.html" title="class in com.mirth.connect.userutil">MessageHeaders</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use getHeader(key) or
             getHeaderList(key) instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageParameters.html#get-java.lang.String-">get(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageParameters.html" title="class in com.mirth.connect.userutil">MessageParameters</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use getParameter(key)
             or getParameterList(key) instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#get-java.lang.Object-">get(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>
<div class="block">Returns the value to which the specified key is mapped, or null if this map contains no
 mapping for the key.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#getAddress--">getAddress()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Returns the server address.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getArray-java.lang.String-">getArray(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getArray-int-">getArray(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getAsciiStream-java.lang.String-">getAsciiStream(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getAsciiStream-int-">getAsciiStream(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#getAttachmentId--">getAttachmentId()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Returns the unique replacement token for the attachment.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableAttachment.html#getAttachmentId--">getAttachmentId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableAttachment.html" title="class in com.mirth.connect.userutil">ImmutableAttachment</a></dt>
<dd>
<div class="block">Returns the unique replacement token for the attachment.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getAttachments--">getAttachments()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Returns a list of attachments associated with this message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getBigDecimal-java.lang.String-int-">getBigDecimal(String, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getBigDecimal-java.lang.String-">getBigDecimal(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getBigDecimal-int-int-">getBigDecimal(int, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getBigDecimal-int-">getBigDecimal(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getBinaryStream-java.lang.String-">getBinaryStream(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getBinaryStream-int-">getBinaryStream(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getBlob-java.lang.String-">getBlob(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getBlob-int-">getBlob(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getBoolean-java.lang.String-">getBoolean(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getBoolean-int-">getBoolean(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getByte-java.lang.String-">getByte(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getByte-int-">getByte(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getBytes-java.lang.String-">getBytes(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getBytes-int-">getBytes(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getChannelId--">getChannelId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the ID of the channel associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getChannelId--">getChannelId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Returns the ID of the channel associated with this message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getChannelIds--">getChannelIds()</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get all channel Ids.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#getChannelMap--">getChannelMap()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="com/mirth/connect/server/userutil/RawMessage.html#getSourceMap--"><code>RawMessage.getSourceMap()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getChannelMap--">getChannelMap()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the channel map.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getChannelName-java.lang.String-">getChannelName(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the name for a channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getChannelName--">getChannelName()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the Name of the channel associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getChannelNames--">getChannelNames()</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get all channels names.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getChannelState-java.lang.String-">getChannelState(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the current state of a channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getCharacterStream-java.lang.String-">getCharacterStream(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getCharacterStream-int-">getCharacterStream(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ContextFactory.html#getClassLoader--">getClassLoader()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ContextFactory.html" title="class in com.mirth.connect.server.userutil">ContextFactory</a></dt>
<dd>
<div class="block">Returns the application classloader that the current JavaScript context is using.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getClob-java.lang.String-">getClob(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getClob-int-">getClob(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getCommand--">getCommand()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getConcurrency--">getConcurrency()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#getConnection--">getConnection()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Returns the database connection (java.sql.Connection) this class is using.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getConnectorMap--">getConnectorMap()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the connector map.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getConnectorMessages--">getConnectorMessages()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Returns a map of connector messages associated with this message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getConnectorName--">getConnectorName()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the name of the connector associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getConnectorState-java.lang.String-java.lang.Number-">getConnectorState(String, Number)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the current state of a connector.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#getContent--">getContent()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Returns the content of the attachment as a byte array.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/AttachmentEntry.html#getContent--">getContent()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/AttachmentEntry.html" title="class in com.mirth.connect.userutil">AttachmentEntry</a></dt>
<dd>
<div class="block">Returns the content of the attachment entry.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableAttachment.html#getContent--">getContent()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableAttachment.html" title="class in com.mirth.connect.userutil">ImmutableAttachment</a></dt>
<dd>
<div class="block">Returns the content of the attachment as a byte array.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getContent-com.mirth.connect.userutil.ContentType-">getContent(ContentType)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">The getContent(contentType) method has been deprecated and will soon be removed.
             Please use getMessageContent(contentType) instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessageContent.html#getContent--">getContent()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></dt>
<dd>
<div class="block">Returns the actual content, as a string.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#getContentString--">getContentString()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Returns the content of the attachment as a string, using UTF-8 encoding.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#getContentString-java.lang.String-">getContentString(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Returns the content of the attachment as a string, using the specified charset encoding.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessageContent.html#getContentType--">getContentType()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></dt>
<dd>
<div class="block">Returns the ContentType of this message content (e.g.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DateUtil.html#getCurrentDate-java.lang.String-">getCurrentDate(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DateUtil.html" title="class in com.mirth.connect.server.userutil">DateUtil</a></dt>
<dd>
<div class="block">Formats the current date into a string according to a specified pattern.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getCursorName--">getCursorName()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getDataSourceName--">getDataSourceName()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessageContent.html#getDataType--">getDataType()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></dt>
<dd>
<div class="block">Returns the data type (e.g.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DateUtil.html#getDate-java.lang.String-java.lang.String-">getDate(String, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DateUtil.html" title="class in com.mirth.connect.server.userutil">DateUtil</a></dt>
<dd>
<div class="block">Parses a date string according to the specified pattern and returns a java.util.Date object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getDate-java.lang.String-">getDate(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getDate-java.lang.String-java.util.Calendar-">getDate(String, Calendar)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getDate-int-">getDate(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getDate-int-java.util.Calendar-">getDate(int, Calendar)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getDefaultDeserializationProperties-java.lang.String-">getDefaultDeserializationProperties(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block">Returns a map of default properties used to customize how deserialization from XML to the
 data type is performed.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getDefaultSerializationProperties-java.lang.String-">getDefaultSerializationProperties(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block">Returns a map of default properties used to customize how serialization from the data type to
 XML is performed.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getDeployedChannelId-java.lang.String-">getDeployedChannelId(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the id for a deployed channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getDeployedChannelIds--">getDeployedChannelIds()</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get all deployed channel Ids.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getDeployedChannelName-java.lang.String-">getDeployedChannelName(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the name for a deployed channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getDeployedChannelNames--">getDeployedChannelNames()</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get all deployed channels names.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getDestinationIdMap--">getDestinationIdMap()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns a Map of destination connector names linked to their corresponding connector metadata
 ID.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getDestinationIdMap--">getDestinationIdMap()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Returns a Map of destination connector names linked to their corresponding connector metadata
 ID.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#getDestinationMetaDataIds--">getDestinationMetaDataIds()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Returns the collection of integers (metadata IDs) representing which destinations to dispatch
 the message to.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getDestinationNameMap--">getDestinationNameMap()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getDestinationIdMap--"><code>getDestinationIdMap()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getDestinationNameMap--">getDestinationNameMap()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="com/mirth/connect/userutil/ImmutableMessage.html#getDestinationIdMap--"><code>getDestinationIdMap()</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#getDICOMMessage-com.mirth.connect.userutil.ImmutableConnectorMessage-">getDICOMMessage(ImmutableConnectorMessage)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Re-attaches DICOM attachments with the header data in the connector message and returns the
 resulting merged data as a byte array.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#getDICOMRawBytes-com.mirth.connect.userutil.ImmutableConnectorMessage-">getDICOMRawBytes(ImmutableConnectorMessage)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Re-attaches DICOM attachments with the header data in the connector message and returns the
 resulting merged data as a byte array.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#getDICOMRawData-com.mirth.connect.userutil.ImmutableConnectorMessage-">getDICOMRawData(ImmutableConnectorMessage)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Re-attaches DICOM attachments with the header data in the connector message and returns the
 resulting merged data as a Base64-encoded string.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getDouble-java.lang.String-">getDouble(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getDouble-int-">getDouble(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getEDISerializer-java.lang.String-java.lang.String-java.lang.String-">getEDISerializer(String, String, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getEncoded--">getEncoded()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves encoded content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getEncodedData--">getEncodedData()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves encoded content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/EncryptedData.html#getEncryptedData--">getEncryptedData()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/EncryptedData.html" title="class in com.mirth.connect.server.userutil">EncryptedData</a></dt>
<dd>
<div class="block">Returns the encrypted data as a byte array.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ImmutableResponse.html#getError--">getError()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ImmutableResponse.html" title="class in com.mirth.connect.server.userutil">ImmutableResponse</a></dt>
<dd>
<div class="block">Returns the error string associated with this response, if it exists.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#getError--">getError()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Returns the error string associated with this response, if it exists.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getErrorCount-java.lang.String-">getErrorCount(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the error count statistic for a specific channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getErrorCount-java.lang.String-java.lang.Number-">getErrorCount(String, Number)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the error count statistic for a specific connector.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ResponseFactory.html#getErrorResponse-java.lang.String-">getErrorResponse(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ResponseFactory.html" title="class in com.mirth.connect.server.userutil">ResponseFactory</a></dt>
<dd>
<div class="block">Returns a Response representing a erred message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getEscapeProcessing--">getEscapeProcessing()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getFetchDirection--">getFetchDirection()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getFetchSize--">getFetchSize()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getFilteredCount-java.lang.String-">getFilteredCount(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the filtered count statistic for a specific channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getFilteredCount-java.lang.String-java.lang.Number-">getFilteredCount(String, Number)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the filtered count statistic for a specific connector.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ResponseFactory.html#getFilteredResponse-java.lang.String-">getFilteredResponse(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ResponseFactory.html" title="class in com.mirth.connect.server.userutil">ResponseFactory</a></dt>
<dd>
<div class="block">Returns a Response representing a filtered message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getFloat-java.lang.String-">getFloat(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getFloat-int-">getFloat(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#getFrom--">getFrom()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Returns the FROM field being used for dispatched e-mail messages.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/EncryptedData.html#getHeader--">getHeader()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/EncryptedData.html" title="class in com.mirth.connect.server.userutil">EncryptedData</a></dt>
<dd>
<div class="block">Returns the meta-information about the encrypted data.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageHeaders.html#getHeader-java.lang.String-">getHeader(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageHeaders.html" title="class in com.mirth.connect.userutil">MessageHeaders</a></dt>
<dd>
<div class="block">Get the first header value for the given key.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageHeaders.html#getHeaderList-java.lang.String-">getHeaderList(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageHeaders.html" title="class in com.mirth.connect.userutil">MessageHeaders</a></dt>
<dd>
<div class="block">Get all header values for the given key.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer-boolean-boolean-boolean-boolean-boolean-">getHL7Serializer(boolean, boolean, boolean, boolean, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer-boolean-boolean-boolean-boolean-">getHL7Serializer(boolean, boolean, boolean, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer-boolean-boolean-boolean-">getHL7Serializer(boolean, boolean, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer-boolean-boolean-">getHL7Serializer(boolean, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getHL7Serializer--">getHL7Serializer()</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead. The new method will now strip namespaces by default unless the
             'stripNamespaces' property is set to false.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getHoldability--">getHoldability()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#getHost--">getHost()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Returns the SMTP server address.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#getId--">getId()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Returns the unique ID for the attachment.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableAttachment.html#getId--">getId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableAttachment.html" title="class in com.mirth.connect.userutil">ImmutableAttachment</a></dt>
<dd>
<div class="block">Returns the unique ID for the attachment.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getImportChannelId--">getImportChannelId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Returns the ID of the original channel this message was reprocessed from.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getImportId--">getImportId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Returns the ID of the original message this one was imported from.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getInt-java.lang.String-">getInt(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getInt-int-">getInt(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ContextFactory.html#getIsolatedClassLoader--">getIsolatedClassLoader()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ContextFactory.html" title="class in com.mirth.connect.server.userutil">ContextFactory</a></dt>
<dd>
<div class="block">Returns a classloader containing only the libraries contained in the custom resources, with
 no parent classloader.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getKeyColumns--">getKeyColumns()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageHeaders.html#getKeys--">getKeys()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageHeaders.html" title="class in com.mirth.connect.userutil">MessageHeaders</a></dt>
<dd>
<div class="block">Get all header keys.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageParameters.html#getKeys--">getKeys()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageParameters.html" title="class in com.mirth.connect.userutil">MessageParameters</a></dt>
<dd>
<div class="block">Get all parameter keys.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getLong-java.lang.String-">getLong(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getLong-int-">getLong(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getMatchColumnIndexes--">getMatchColumnIndexes()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getMatchColumnNames--">getMatchColumnNames()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getMaxFieldSize--">getMaxFieldSize()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getMaxRows--">getMaxRows()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getMergedConnectorMessage--">getMergedConnectorMessage()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Returns a "merged" connector message containing data from all connector messages combined.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ImmutableResponse.html#getMessage--">getMessage()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ImmutableResponse.html" title="class in com.mirth.connect.server.userutil">ImmutableResponse</a></dt>
<dd>
<div class="block">Returns the actual response data, as a string.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#getMessage--">getMessage()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Returns the actual response data, as a string.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-">getMessageAttachment(ImmutableConnectorMessage, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Retrieves an attachment from the current channel/message ID.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-boolean-">getMessageAttachment(ImmutableConnectorMessage, String, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Retrieves an attachment from the current channel/message ID.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachment-java.lang.String-java.lang.Long-java.lang.String-">getMessageAttachment(String, Long, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Retrieves an attachment from a specific channel/message ID.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachment-java.lang.String-java.lang.Long-java.lang.String-boolean-">getMessageAttachment(String, Long, String, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Retrieves an attachment from a specific channel/message ID.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachmentIds-com.mirth.connect.userutil.ImmutableConnectorMessage-">getMessageAttachmentIds(ImmutableConnectorMessage)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Returns a List of attachment IDs associated with the current channel / message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachmentIds-java.lang.String-java.lang.Long-">getMessageAttachmentIds(String, Long)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Returns a List of attachment IDs associated with the current channel / message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachments-com.mirth.connect.userutil.ImmutableConnectorMessage-">getMessageAttachments(ImmutableConnectorMessage)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Retrieves all attachments associated with a connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachments-com.mirth.connect.userutil.ImmutableConnectorMessage-boolean-">getMessageAttachments(ImmutableConnectorMessage, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Retrieves all attachments associated with a connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachments-java.lang.String-java.lang.Long-">getMessageAttachments(String, Long)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Retrieves all attachments associated with a specific channel/message ID.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachments-java.lang.String-java.lang.Long-boolean-">getMessageAttachments(String, Long, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Retrieves all attachments associated with a specific channel/message ID.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachmentsFromSourceChannel-com.mirth.connect.userutil.ImmutableConnectorMessage-">getMessageAttachmentsFromSourceChannel(ImmutableConnectorMessage)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Retrieves an attachment from an upstream channel that sent a message to the current channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachmentsFromSourceChannel-com.mirth.connect.userutil.ImmutableConnectorMessage-boolean-">getMessageAttachmentsFromSourceChannel(ImmutableConnectorMessage, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Retrieves an attachment from an upstream channel that sent a message to the current channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getMessageContent-com.mirth.connect.userutil.ContentType-">getMessageContent(ContentType)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getMessageId--">getMessageId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the sequential ID of the overall Message associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getMessageId--">getMessageId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Returns the sequential ID of this message, as a Long.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessageContent.html#getMessageId--">getMessageId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></dt>
<dd>
<div class="block">Returns the sequential ID of the overall Message associated with this message content.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getMetaData--">getMetaData()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getMetaDataId--">getMetaDataId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the metadata ID of this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessageContent.html#getMetaDataId--">getMetaDataId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></dt>
<dd>
<div class="block">Returns the metadata ID of the connector associated with this message content.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/AttachmentEntry.html#getMimeType--">getMimeType()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/AttachmentEntry.html" title="class in com.mirth.connect.userutil">AttachmentEntry</a></dt>
<dd>
<div class="block">Returns the MIME type of the attachment entry.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/AttachmentEntry.html#getName--">getName()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/AttachmentEntry.html" title="class in com.mirth.connect.userutil">AttachmentEntry</a></dt>
<dd>
<div class="block">Returns the name of the attachment entry.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getNCharacterStream-int-">getNCharacterStream(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getNCharacterStream-java.lang.String-">getNCharacterStream(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getNClob-int-">getNClob(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getNClob-java.lang.String-">getNClob(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getNCPDPSerializer-java.lang.String-java.lang.String-java.lang.String-boolean-">getNCPDPSerializer(String, String, String, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getNCPDPSerializer-java.lang.String-java.lang.String-java.lang.String-">getNCPDPSerializer(String, String, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ImmutableResponse.html#getNewMessageStatus--">getNewMessageStatus()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ImmutableResponse.html" title="class in com.mirth.connect.server.userutil">ImmutableResponse</a></dt>
<dd>
<div class="block">Returns the Status (e.g.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getNString-int-">getNString(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getNString-java.lang.String-">getNString(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getObject-java.lang.String-">getObject(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getObject-java.lang.String-java.util.Map-">getObject(String, Map&lt;String, Class&lt;?&gt;&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getObject-int-">getObject(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getObject-int-java.util.Map-">getObject(int, Map&lt;String, Class&lt;?&gt;&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getObject-int-java.lang.Class-">getObject(int, Class&lt;T&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getObject-java.lang.String-java.lang.Class-">getObject(String, Class&lt;T&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getOriginal--">getOriginal()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getOriginalId--">getOriginalId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Returns the ID of the original message this one was reprocessed from.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getOriginalRow--">getOriginalRow()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getPageSize--">getPageSize()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageParameters.html#getParameter-java.lang.String-">getParameter(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageParameters.html" title="class in com.mirth.connect.userutil">MessageParameters</a></dt>
<dd>
<div class="block">Get the first parameter value for the given key.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageParameters.html#getParameterList-java.lang.String-">getParameterList(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageParameters.html" title="class in com.mirth.connect.userutil">MessageParameters</a></dt>
<dd>
<div class="block">Get all parameter values for the given key.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getPassword--">getPassword()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#getPassword--">getPassword()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Returns the password being used to authenticate to the SMTP server.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#getPort--">getPort()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Returns the SMTP server port.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getPostProcessorError--">getPostProcessorError()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the postprocessing error string associated with this connector message, if it exists.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getProcessedRaw--">getProcessedRaw()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves processed raw content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getProcessedRawData--">getProcessedRawData()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves processed raw content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getProcessedResponse--">getProcessedResponse()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves processed response content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getProcessedResponseData--">getProcessedResponseData()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves processed response content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getProcessingError--">getProcessingError()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the processing error string associated with this connector message, if it exists.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getQueryTimeout--">getQueryTimeout()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getQueuedCount-java.lang.String-">getQueuedCount(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the queued count statistic for a specific channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getQueuedCount-java.lang.String-java.lang.Number-">getQueuedCount(String, Number)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the queued count statistic for a specific connector.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ResponseFactory.html#getQueuedResponse-java.lang.String-">getQueuedResponse(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ResponseFactory.html" title="class in com.mirth.connect.server.userutil">ResponseFactory</a></dt>
<dd>
<div class="block">Returns a Response representing a queued message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getRaw--">getRaw()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves raw content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#getRawBytes--">getRawBytes()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Returns the binary data (byte array) to be dispatched to a channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#getRawData--">getRawData()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Returns the textual data to be dispatched to a channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getRawData--">getRawData()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves raw content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#getRealm--">getRealm()</a></span> - Method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Returns the realm that the request has been authenticated with.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getReceivedCount-java.lang.String-">getReceivedCount(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the received count statistic for a specific channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getReceivedCount-java.lang.String-java.lang.Number-">getReceivedCount(String, Number)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the received count statistic for a specific connector.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getReceivedDate--">getReceivedDate()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the date/time that this connector message was created by the channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getReceivedDate--">getReceivedDate()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. This method currently returns
             the received date of the source connector message.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getRef-java.lang.String-">getRef(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getRef-int-">getRef(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ContextFactory.html#getResourceIds--">getResourceIds()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ContextFactory.html" title="class in com.mirth.connect.server.userutil">ContextFactory</a></dt>
<dd>
<div class="block">Returns the set of custom resource IDs that the current JavaScript context is using.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponse--">getResponse()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves response content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponseData--">getResponseData()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves response content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponseDate--">getResponseDate()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the date/time immediately after this connector message's response is received.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponseError--">getResponseError()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the response error string associated with this connector message, if it exists.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#getResponseHeaders--">getResponseHeaders()</a></span> - Method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Returns the map of HTTP headers to be sent along with the authentication response.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponseMap--">getResponseMap()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the response map.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponseTransformed--">getResponseTransformed()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves transformed response content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponseTransformedData--">getResponseTransformedData()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves transformed response content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getRow--">getRow()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getRowId-int-">getRowId(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getRowId-java.lang.String-">getRowId(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getRowSetWarnings--">getRowSetWarnings()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#getSecure--">getSecure()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Returns the encryption security layer being used for the SMTP connection (e.g "TLS" or
 "SSL").</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getSendAttempts--">getSendAttempts()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the number of times this message has been attempted to be dispatched by the
 connector.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getSendDate--">getSendDate()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the date/time immediately before this connector message's most recent send attempt.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getSentCount-java.lang.String-">getSentCount(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the sent count statistic for a specific channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#getSentCount-java.lang.String-java.lang.Number-">getSentCount(String, Number)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Get the sent count statistic for a specific connector.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ResponseFactory.html#getSentResponse-java.lang.String-">getSentResponse(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ResponseFactory.html" title="class in com.mirth.connect.server.userutil">ResponseFactory</a></dt>
<dd>
<div class="block">Returns a Response representing a successfully sent message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getSerializer-java.lang.String-">getSerializer(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block">Returns a serializer (with toXML and fromXML methods) for a given data type.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getSerializer-java.lang.String-java.util.Map-java.util.Map-">getSerializer(String, Map&lt;String, Object&gt;, Map&lt;String, Object&gt;)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block">Returns a serializer (with toXML and fromXML methods) for a given data type and properties.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getServerId--">getServerId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the ID of the server associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#getServerId--">getServerId()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Returns the ID of the server associated with this message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getShort-java.lang.String-">getShort(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getShort-int-">getShort(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getShowDeleted--">getShowDeleted()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#getSliceCount-com.mirth.connect.userutil.ImmutableConnectorMessage-">getSliceCount(ImmutableConnectorMessage)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Returns the number of slices in the fully-merged DICOM data associated with a given connector
 message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#getSocketTimeout--">getSocketTimeout()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Returns the socket connection timeout value in milliseconds.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#getSourceMap--">getSourceMap()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Returns the source map to be used at the beginning of the channel dispatch.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getSourceMap--">getSourceMap()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the source map.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getSQLXML-int-">getSQLXML(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getSQLXML-java.lang.String-">getSQLXML(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getStatement--">getStatement()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#getStatus--">getStatus()</a></span> - Method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Returns the accept/reject status of the authentication attempt.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getStatus--">getStatus()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns the status (e.g.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#getStatus--">getStatus()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Returns the Status (e.g.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ImmutableResponse.html#getStatusMessage--">getStatusMessage()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ImmutableResponse.html" title="class in com.mirth.connect.server.userutil">ImmutableResponse</a></dt>
<dd>
<div class="block">Returns a brief message explaining the reason for the current status.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#getStatusMessage--">getStatusMessage()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Returns a brief message explaining the reason for the current status.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getString-java.lang.String-">getString(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getString-int-">getString(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getSyncProvider--">getSyncProvider()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getTableName--">getTableName()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getTime-java.lang.String-">getTime(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getTime-java.lang.String-java.util.Calendar-">getTime(String, Calendar)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getTime-int-">getTime(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getTime-int-java.util.Calendar-">getTime(int, Calendar)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getTimestamp-java.lang.String-">getTimestamp(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getTimestamp-java.lang.String-java.util.Calendar-">getTimestamp(String, Calendar)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getTimestamp-int-">getTimestamp(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getTimestamp-int-java.util.Calendar-">getTimestamp(int, Calendar)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getTransactionIsolation--">getTransactionIsolation()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getTransformed--">getTransformed()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves transformed content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#getTransformedData--">getTransformedData()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Retrieves transformed content associated with this connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#getType--">getType()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Returns the MIME type of the attachment.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getType--">getType()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableAttachment.html#getType--">getType()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableAttachment.html" title="class in com.mirth.connect.userutil">ImmutableAttachment</a></dt>
<dd>
<div class="block">Returns the MIME type of the attachment.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getTypeMap--">getTypeMap()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getUnicodeStream-java.lang.String-">getUnicodeStream(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getUnicodeStream-int-">getUnicodeStream(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getURL-java.lang.String-">getURL(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getUrl--">getUrl()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getURL-int-">getURL(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#getUsername--">getUsername()</a></span> - Method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Returns the username that the request has been authenticated with.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getUsername--">getUsername()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#getUsername--">getUsername()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Returns the username being used to authenticate to the SMTP server.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/UUIDGenerator.html#getUUID--">getUUID()</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/UUIDGenerator.html" title="class in com.mirth.connect.server.userutil">UUIDGenerator</a></dt>
<dd>
<div class="block">Returns a type 4 (pseudo randomly generated) UUID.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#getWarnings--">getWarnings()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SerializerFactory.html#getX12Serializer-boolean-">getX12Serializer(boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             getSerializer(dataType, serializationPropertiesMap, deserializationPropertiesMap)
             instead.</span></div>
</div>
</dd>
</dl>
<a name="I:H">
<!--   -->
</a>
<h2 class="title">H</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#haltChannel-java.lang.String-">haltChannel(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Halt a deployed channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#hashCode--">hashCode()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#hashCode--">hashCode()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#hashCode--">hashCode()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns a hash code value for the object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#hashCode--">hashCode()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Returns a hash code value for the object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#hashCode--">hashCode()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/mirth/connect/server/userutil/HashUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">HashUtil</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Provides hash utility methods.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/HTTPUtil.html#httpBodyToXml-java.io.InputStream-java.lang.String-">httpBodyToXml(InputStream, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/HTTPUtil.html" title="class in com.mirth.connect.server.userutil">HTTPUtil</a></dt>
<dd>
<div class="block">Serializes an HTTP request body into XML.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/HTTPUtil.html#httpBodyToXml-java.lang.String-java.lang.String-">httpBodyToXml(String, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/HTTPUtil.html" title="class in com.mirth.connect.server.userutil">HTTPUtil</a></dt>
<dd>
<div class="block">Serializes an HTTP request body into XML.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/HTTPUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">HTTPUtil</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Provides HTTP utility methods.</div>
</dd>
</dl>
<a name="I:I">
<!--   -->
</a>
<h2 class="title">I</h2>
<dl>
<dt><a href="com/mirth/connect/userutil/ImmutableAttachment.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">ImmutableAttachment</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">This class represents an message attachment and is used to retrieve details such as the
 replacement token or content type.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableAttachment.html#ImmutableAttachment-com.mirth.connect.donkey.model.message.attachment.Attachment-">ImmutableAttachment(Attachment)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableAttachment.html" title="class in com.mirth.connect.userutil">ImmutableAttachment</a></dt>
<dd>
<div class="block">Instantiates a new ImmutableAttachment object.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">ImmutableConnectorMessage</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">This class represents a connector message and is used to retrieve details such as the message ID,
 metadata ID, status, and various content types.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#ImmutableConnectorMessage-com.mirth.connect.donkey.model.message.ConnectorMessage-">ImmutableConnectorMessage(ConnectorMessage)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Instantiates a new ImmutableConnectorMessage object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#ImmutableConnectorMessage-com.mirth.connect.donkey.model.message.ConnectorMessage-boolean-">ImmutableConnectorMessage(ConnectorMessage, boolean)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Instantiates a new ImmutableConnectorMessage object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#ImmutableConnectorMessage-com.mirth.connect.donkey.model.message.ConnectorMessage-boolean-java.util.Map-">ImmutableConnectorMessage(ConnectorMessage, boolean, Map&lt;String, Integer&gt;)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Instantiates a new ImmutableConnectorMessage object.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">ImmutableMessage</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">This class represents an overall message and is used to retrieve details such as the message ID,
 specific connector messages, or the merged connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#ImmutableMessage-com.mirth.connect.donkey.model.message.Message-">ImmutableMessage(Message)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Instantiates a new ImmutableMessage object.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">ImmutableMessageContent</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">This class represents content associated with a connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessageContent.html#ImmutableMessageContent-com.mirth.connect.donkey.model.message.MessageContent-">ImmutableMessageContent(MessageContent)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></dt>
<dd>
<div class="block">Instantiates a new ImmutableMessageContent object.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/ImmutableResponse.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">ImmutableResponse</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">This class represents a destination response and is used to retrieve details such as the response
 data, message status, and errors.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ImmutableResponse.html#ImmutableResponse-com.mirth.connect.userutil.Response-">ImmutableResponse(Response)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ImmutableResponse.html" title="class in com.mirth.connect.server.userutil">ImmutableResponse</a></dt>
<dd>
<div class="block">Instantiates a new ImmutableResponse object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#indexOf-java.lang.Object-">indexOf(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns the index of the first occurrence of the specified element
 in this list, or -1 if this list does not contain the element.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnectionFactory.html#initializeDriver-java.lang.String-">initializeDriver(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnectionFactory.html" title="class in com.mirth.connect.server.userutil">DatabaseConnectionFactory</a></dt>
<dd>
<div class="block">Initializes the specified JDBC driver.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#insertRow--">insertRow()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#isAfterLast--">isAfterLast()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#isBeforeFirst--">isBeforeFirst()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#isBinary--">isBinary()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Returns a Boolean representing whether this object contains textual or binary data.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Future.html#isCancelled--">isCancelled()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a></dt>
<dd>
<div class="block">Returns <code>true</code> if this task was cancelled before it completed normally.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#isChannelDeployed-java.lang.String-">isChannelDeployed(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Check if a channel is currently deployed.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#isClosed--">isClosed()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Future.html#isDone--">isDone()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a></dt>
<dd>
<div class="block">Returns <code>true</code> if this task completed.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#isEmpty--">isEmpty()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#isEmpty--">isEmpty()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#isEmpty--">isEmpty()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns <tt>true</tt> if this list contains no elements.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#isEmpty--">isEmpty()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Returns <tt>true</tt> if this map contains no key-value mappings.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#isEmpty--">isEmpty()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableAttachment.html#isEncrypted--">isEncrypted()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableAttachment.html" title="class in com.mirth.connect.userutil">ImmutableAttachment</a></dt>
<dd>
<div class="block">Returns a boolean indicating whether the attachment content is encrypted.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#isFirst--">isFirst()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#isLast--">isLast()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#isProcessed--">isProcessed()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Returns whether this message has finished processing through a channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#isReadOnly--">isReadOnly()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#isUseAuthentication--">isUseAuthentication()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Returns true if authentication is needed for the SMTP server, otherwise returns false.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#isWrapperFor-java.lang.Class-">isWrapperFor(Class&lt;?&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#iterator--">iterator()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns an iterator over the elements in this list in proper sequence.</div>
</dd>
</dl>
<a name="I:J">
<!--   -->
</a>
<h2 class="title">J</h2>
<dl>
<dt><a href="com/mirth/connect/userutil/JsonUtil.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">JsonUtil</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">Provides JSON utility methods.</div>
</dd>
</dl>
<a name="I:K">
<!--   -->
</a>
<h2 class="title">K</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#keySet--">keySet()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#keySet--">keySet()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#keySet--">keySet()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Returns a <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Set.html?is-external=true" title="class or interface in java.util"><code>Set</code></a> view of the keys contained in this map.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#keySet--">keySet()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:L">
<!--   -->
</a>
<h2 class="title">L</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#last--">last()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#lastIndexOf-java.lang.Object-">lastIndexOf(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns the index of the last occurrence of the specified element
 in this list, or -1 if this list does not contain the element.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Lists.html#list--">list()</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Lists.html" title="class in com.mirth.connect.userutil">Lists</a></dt>
<dd>
<div class="block">Instantiates a new <a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil"><code>ListBuilder</code></a> using an ArrayList.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Lists.html#list-java.lang.Object-">list(Object)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Lists.html" title="class in com.mirth.connect.userutil">Lists</a></dt>
<dd>
<div class="block">Instantiates a new <a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil"><code>ListBuilder</code></a> using an ArrayList and the given element.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Lists.html#list-java.util.List-">list(List)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Lists.html" title="class in com.mirth.connect.userutil">Lists</a></dt>
<dd>
<div class="block">Instantiates a new <a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil"><code>ListBuilder</code></a> using the given list.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">ListBuilder</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">Convenience class to allow fluent building of lists.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#listIterator--">listIterator()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns a list iterator over the elements in this list (in proper
 sequence).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#listIterator-int-">listIterator(int)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns a list iterator over the elements in this list (in proper
 sequence), starting at the specified position in the list.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/Lists.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Lists</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">Convenience class to allow fluent building of lists.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Lists.html#Lists--">Lists()</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Lists.html" title="class in com.mirth.connect.userutil">Lists</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:M">
<!--   -->
</a>
<h2 class="title">M</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Maps.html#map--">map()</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Maps.html" title="class in com.mirth.connect.userutil">Maps</a></dt>
<dd>
<div class="block">Instantiates a new <a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil"><code>MapBuilder</code></a> using a HashMap.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Maps.html#map-java.lang.Object-java.lang.Object-">map(Object, Object)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Maps.html" title="class in com.mirth.connect.userutil">Maps</a></dt>
<dd>
<div class="block">Instantiates a new <a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil"><code>MapBuilder</code></a> using a HashMap and the given key/value entry.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Maps.html#map-java.util.Map-">map(Map)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Maps.html" title="class in com.mirth.connect.userutil">Maps</a></dt>
<dd>
<div class="block">Instantiates a new <a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil"><code>MapBuilder</code></a> using the given map.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">MapBuilder</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">Convenience class to allow fluent building of maps.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/Maps.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Maps</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">Convenience class to allow fluent building of maps.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Maps.html#Maps--">Maps()</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Maps.html" title="class in com.mirth.connect.userutil">Maps</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#mergeHeaderAttachments-com.mirth.connect.userutil.ImmutableConnectorMessage-java.util.List-">mergeHeaderAttachments(ImmutableConnectorMessage, List&lt;Attachment&gt;)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Re-attaches DICOM attachments with the header data in the connector message and returns the
 resulting merged data as a Base-64 encoded String.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DICOMUtil.html#mergeHeaderPixelData-byte:A-java.util.List-">mergeHeaderPixelData(byte[], List&lt;byte[]&gt;)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></dt>
<dd>
<div class="block">Re-attaches DICOM attachments with the given header data and returns the resulting merged
 data as a Base-64 encoded String.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/MessageHeaders.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">MessageHeaders</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageHeaders.html#MessageHeaders-java.util.Map-">MessageHeaders(Map&lt;String, List&lt;String&gt;&gt;)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageHeaders.html" title="class in com.mirth.connect.userutil">MessageHeaders</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/mirth/connect/userutil/MessageParameters.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">MessageParameters</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageParameters.html#MessageParameters-java.util.Map-">MessageParameters(Map&lt;String, List&lt;String&gt;&gt;)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageParameters.html" title="class in com.mirth.connect.userutil">MessageParameters</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">MirthCachedRowSet</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">An implementation of CachedRowSet that retrieves values based on the column label value.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#MirthCachedRowSet--">MirthCachedRowSet()</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#moveToCurrentRow--">moveToCurrentRow()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#moveToInsertRow--">moveToInsertRow()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:N">
<!--   -->
</a>
<h2 class="title">N</h2>
<dl>
<dt><a href="com/mirth/connect/server/userutil/NCPDPUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">NCPDPUtil</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Provides NCPDP utility methods.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#next--">next()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#nextPage--">nextPage()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:P">
<!--   -->
</a>
<h2 class="title">P</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/HTTPUtil.html#parseHeaders-java.lang.String-">parseHeaders(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/HTTPUtil.html" title="class in com.mirth.connect.server.userutil">HTTPUtil</a></dt>
<dd>
<div class="block">Converts a block of HTTP header fields into a Map containing each header key and value.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#pauseChannel-java.lang.String-">pauseChannel(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Pause a deployed channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#populate-java.sql.ResultSet-">populate(ResultSet)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#populate-java.sql.ResultSet-int-">populate(ResultSet, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/JsonUtil.html#prettyPrint-java.lang.String-">prettyPrint(String)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/JsonUtil.html" title="class in com.mirth.connect.userutil">JsonUtil</a></dt>
<dd>
<div class="block">Formats an JSON string with indented markup.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/XmlUtil.html#prettyPrint-java.lang.String-">prettyPrint(String)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/XmlUtil.html" title="class in com.mirth.connect.userutil">XmlUtil</a></dt>
<dd>
<div class="block">Formats an XML string with indented markup.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#previous--">previous()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#previousPage--">previousPage()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#put-java.lang.String-java.lang.Object-">put(String, Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#put-java.lang.String-java.lang.Object-">put(String, Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#put-java.lang.Object-java.lang.Object-">put(Object, Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Associates the specified value with the specified key in this map
 (optional operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#put-java.lang.String-java.lang.Object-">put(String, Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#putAll-java.util.Map-">putAll(Map&lt;? extends String, ? extends Object&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#putAll-java.util.Map-">putAll(Map&lt;? extends String, ? extends Object&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#putAll-java.util.Map-">putAll(Map)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Copies all of the mappings from the specified map to this map
 (optional operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#putAll-java.util.Map-">putAll(Map&lt;? extends String, ? extends Object&gt;)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
</dl>
<a name="I:R">
<!--   -->
</a>
<h2 class="title">R</h2>
<dl>
<dt><a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">RawMessage</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">This class represents a raw message as it is received by a channel, and is used to retrieve
 details such as the raw data or source map.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#RawMessage-java.lang.String-">RawMessage(String)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#RawMessage-java.lang.String-java.util.Collection-">RawMessage(String, Collection&lt;Number&gt;)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#RawMessage-java.lang.String-java.util.Collection-java.util.Map-">RawMessage(String, Collection&lt;Number&gt;, Map&lt;String, Object&gt;)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#RawMessage-byte:A-">RawMessage(byte[])</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#RawMessage-byte:A-java.util.Collection-">RawMessage(byte[], Collection&lt;Number&gt;)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#RawMessage-byte:A-java.util.Collection-java.util.Map-">RawMessage(byte[], Collection&lt;Number&gt;, Map&lt;String, Object&gt;)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/FileUtil.html#read-java.lang.String-">read(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/FileUtil.html" title="class in com.mirth.connect.server.userutil">FileUtil</a></dt>
<dd>
<div class="block">Returns the contents of the file as a string, using the system default charset encoding.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/FileUtil.html#readBytes-java.lang.String-">readBytes(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/FileUtil.html" title="class in com.mirth.connect.server.userutil">FileUtil</a></dt>
<dd>
<div class="block">Returns the contents of the file as a byte array.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#reAttachMessage-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-boolean-">reAttachMessage(String, ImmutableConnectorMessage, String, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Replaces any unique attachment tokens (e.g.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#reAttachMessage-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-boolean-boolean-boolean-">reAttachMessage(String, ImmutableConnectorMessage, String, boolean, boolean, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Replaces any unique attachment tokens (e.g.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#reAttachMessage-com.mirth.connect.userutil.ImmutableConnectorMessage-">reAttachMessage(ImmutableConnectorMessage)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Replaces any unique attachment tokens (e.g.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#reAttachMessage-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-">reAttachMessage(String, ImmutableConnectorMessage)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Replaces any unique attachment tokens (e.g.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#refreshRow--">refreshRow()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#relative-int-">relative(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#release--">release()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#remove-java.lang.Object-">remove(Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DestinationSet.html#remove-java.lang.Object-">remove(Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DestinationSet.html" title="class in com.mirth.connect.server.userutil">DestinationSet</a></dt>
<dd>
<div class="block">Stop a destination from being processed for this message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DestinationSet.html#remove-java.util.Collection-">remove(Collection&lt;Object&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DestinationSet.html" title="class in com.mirth.connect.server.userutil">DestinationSet</a></dt>
<dd>
<div class="block">Stop a destination from being processed for this message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#remove-java.lang.Object-">remove(Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#remove-java.lang.Object-">remove(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Removes the first occurrence of the specified element from this list,
 if it is present (optional operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#remove-int-">remove(int)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Removes the element at the specified position in this list (optional
 operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#remove-java.lang.Object-">remove(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Removes the mapping for a key from this map if it is present
 (optional operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#remove-java.lang.Object-">remove(Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DestinationSet.html#removeAll--">removeAll()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DestinationSet.html" title="class in com.mirth.connect.server.userutil">DestinationSet</a></dt>
<dd>
<div class="block">Stop all destinations from being processed for this message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#removeAll-java.util.Collection-">removeAll(Collection)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Removes from this list all of its elements that are contained in the
 specified collection (optional operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DestinationSet.html#removeAllExcept-java.lang.Object-">removeAllExcept(Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DestinationSet.html" title="class in com.mirth.connect.server.userutil">DestinationSet</a></dt>
<dd>
<div class="block">Stop all except one destination from being processed for this message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DestinationSet.html#removeAllExcept-java.util.Collection-">removeAllExcept(Collection&lt;Object&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DestinationSet.html" title="class in com.mirth.connect.server.userutil">DestinationSet</a></dt>
<dd>
<div class="block">Stop all except one destination from being processed for this message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#removeRowSetListener-javax.sql.RowSetListener-">removeRowSetListener(RowSetListener)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#resetStatistics-java.lang.String-">resetStatistics(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Reset all statistics for a specific channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#resetStatistics-java.lang.String-java.lang.Integer-">resetStatistics(String, Integer)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Reset all statistics for the specified connector on the given channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#resetStatistics-java.lang.String-java.lang.Integer-java.util.Collection-">resetStatistics(String, Integer, Collection&lt;Status&gt;)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Reset the specified statistics for the specified connector on the given channel.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Response</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">This class represents a channel or destination response and is used to retrieve details such as
 the response data, message status, and errors.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#Response--">Response()</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Instantiates a new Response object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#Response-java.lang.String-">Response(String)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Instantiates a new Response object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#Response-com.mirth.connect.userutil.Status-java.lang.String-">Response(Status, String)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Instantiates a new Response object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#Response-com.mirth.connect.userutil.Status-java.lang.String-java.lang.String-">Response(Status, String, String)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Instantiates a new Response object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#Response-com.mirth.connect.userutil.Status-java.lang.String-java.lang.String-java.lang.String-">Response(Status, String, String, String)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Instantiates a new Response object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#Response-com.mirth.connect.donkey.model.message.Response-">Response(Response)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Instantiates a new Response object.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/ResponseFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">ResponseFactory</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Provides methods to create Response objects.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">ResponseMap</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">A wrapper class for the response map which allows users to retrieve values using the proper "d#"
 key (where "#" is the destination connector's metadata ID), or by using the actual destination
 name.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#ResponseMap-java.util.Map-java.util.Map-">ResponseMap(Map&lt;String, Object&gt;, Map&lt;String, Integer&gt;)</a></span> - Constructor for class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>
<div class="block">Instantiates a new ResponseMap object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#restoreOriginal--">restoreOriginal()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#resumeChannel-java.lang.String-">resumeChannel(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Resume a deployed channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#retainAll-java.util.Collection-">retainAll(Collection)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Retains only the elements in this list that are contained in the
 specified collection (optional operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#rollback--">rollback()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Undoes all changes made in the current transaction and releases any database locks currently
 held by this Connection object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#rollback--">rollback()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#rollback-java.sql.Savepoint-">rollback(Savepoint)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/VMRouter.html#routeMessage-java.lang.String-java.lang.String-">routeMessage(String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/VMRouter.html" title="class in com.mirth.connect.server.userutil">VMRouter</a></dt>
<dd>
<div class="block">Dispatches a message to a channel, specified by the deployed channel name.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/VMRouter.html#routeMessage-java.lang.String-com.mirth.connect.server.userutil.RawMessage-">routeMessage(String, RawMessage)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/VMRouter.html" title="class in com.mirth.connect.server.userutil">VMRouter</a></dt>
<dd>
<div class="block">Dispatches a message to a channel, specified by the deployed channel name.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/VMRouter.html#routeMessageByChannelId-java.lang.String-java.lang.String-">routeMessageByChannelId(String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/VMRouter.html" title="class in com.mirth.connect.server.userutil">VMRouter</a></dt>
<dd>
<div class="block">Dispatches a message to a channel, specified by the deployed channel ID.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/VMRouter.html#routeMessageByChannelId-java.lang.String-com.mirth.connect.server.userutil.RawMessage-">routeMessageByChannelId(String, RawMessage)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/VMRouter.html" title="class in com.mirth.connect.server.userutil">VMRouter</a></dt>
<dd>
<div class="block">Dispatches a message to a channel, specified by the deployed channel ID.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#rowDeleted--">rowDeleted()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#rowInserted--">rowInserted()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#rowSetPopulated-javax.sql.RowSetEvent-int-">rowSetPopulated(RowSetEvent, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#rowUpdated--">rowUpdated()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/FileUtil.html#rtfToPlainText-java.lang.String-java.lang.String-">rtfToPlainText(String, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/FileUtil.html" title="class in com.mirth.connect.server.userutil">FileUtil</a></dt>
<dd>
<div class="block">Converts an RTF into plain text using the Swing RTFEditorKit.</div>
</dd>
</dl>
<a name="I:S">
<!--   -->
</a>
<h2 class="title">S</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#send-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">send(String, String, String, String, String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Sends an e-mail message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#send-java.lang.String-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">send(String, String, String, String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Sends an e-mail message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#send-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">send(String, String, String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Sends an e-mail message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AlertSender.html#sendAlert-java.lang.String-">sendAlert(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AlertSender.html" title="class in com.mirth.connect.server.userutil">AlertSender</a></dt>
<dd>
<div class="block">Dispatches an error event that can be alerted on.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">SerializerFactory</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Used to create a serializer for a specific data type for conversion to and from XML.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#set-int-java.lang.Object-">set(int, Object)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Replaces the element at the specified position in this list with the
 specified element (optional operation).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setArray-int-java.sql.Array-">setArray(int, Array)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setAsciiStream-int-java.io.InputStream-int-">setAsciiStream(int, InputStream, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setAsciiStream-java.lang.String-java.io.InputStream-int-">setAsciiStream(String, InputStream, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setAsciiStream-int-java.io.InputStream-">setAsciiStream(int, InputStream)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setAsciiStream-java.lang.String-java.io.InputStream-">setAsciiStream(String, InputStream)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DatabaseConnection.html#setAutoCommit-boolean-">setAutoCommit(boolean)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></dt>
<dd>
<div class="block">Sets this connection's auto-commit mode to the given state.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBigDecimal-int-java.math.BigDecimal-">setBigDecimal(int, BigDecimal)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBigDecimal-java.lang.String-java.math.BigDecimal-">setBigDecimal(String, BigDecimal)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBinaryStream-int-java.io.InputStream-int-">setBinaryStream(int, InputStream, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBinaryStream-java.lang.String-java.io.InputStream-int-">setBinaryStream(String, InputStream, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBinaryStream-int-java.io.InputStream-">setBinaryStream(int, InputStream)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBinaryStream-java.lang.String-java.io.InputStream-">setBinaryStream(String, InputStream)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBlob-int-java.sql.Blob-">setBlob(int, Blob)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBlob-int-java.io.InputStream-long-">setBlob(int, InputStream, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBlob-int-java.io.InputStream-">setBlob(int, InputStream)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBlob-java.lang.String-java.io.InputStream-long-">setBlob(String, InputStream, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBlob-java.lang.String-java.sql.Blob-">setBlob(String, Blob)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBlob-java.lang.String-java.io.InputStream-">setBlob(String, InputStream)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBoolean-int-boolean-">setBoolean(int, boolean)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBoolean-java.lang.String-boolean-">setBoolean(String, boolean)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setByte-int-byte-">setByte(int, byte)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setByte-java.lang.String-byte-">setByte(String, byte)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBytes-int-byte:A-">setBytes(int, byte[])</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setBytes-java.lang.String-byte:A-">setBytes(String, byte[])</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#setChannelMap-java.util.Map-">setChannelMap(Map&lt;String, Object&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="com/mirth/connect/server/userutil/RawMessage.html#setSourceMap-java.util.Map-"><code>setSourceMap(sourceMap)</code></a> instead.</span></div>
</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setCharacterStream-int-java.io.Reader-int-">setCharacterStream(int, Reader, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setCharacterStream-java.lang.String-java.io.Reader-int-">setCharacterStream(String, Reader, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setCharacterStream-int-java.io.Reader-">setCharacterStream(int, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setCharacterStream-java.lang.String-java.io.Reader-">setCharacterStream(String, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setClob-int-java.sql.Clob-">setClob(int, Clob)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setClob-int-java.io.Reader-long-">setClob(int, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setClob-int-java.io.Reader-">setClob(int, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setClob-java.lang.String-java.io.Reader-long-">setClob(String, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setClob-java.lang.String-java.sql.Clob-">setClob(String, Clob)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setClob-java.lang.String-java.io.Reader-">setClob(String, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setCommand-java.lang.String-">setCommand(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setConcurrency-int-">setConcurrency(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#setContent-byte:A-">setContent(byte[])</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Sets the content of the attachment.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/AttachmentEntry.html#setContent-java.lang.String-">setContent(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/AttachmentEntry.html" title="class in com.mirth.connect.userutil">AttachmentEntry</a></dt>
<dd>
<div class="block">Sets the content of the attachment entry.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#setContentString-java.lang.String-">setContentString(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Sets the content of the attachment, using UTF-8 encoding.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#setContentString-java.lang.String-java.lang.String-">setContentString(String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Sets the content of the attachment, using the specified charset encoding.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setDataSourceName-java.lang.String-">setDataSourceName(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setDate-int-java.sql.Date-">setDate(int, Date)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setDate-int-java.sql.Date-java.util.Calendar-">setDate(int, Date, Calendar)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setDate-java.lang.String-java.sql.Date-">setDate(String, Date)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setDate-java.lang.String-java.sql.Date-java.util.Calendar-">setDate(String, Date, Calendar)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#setDestinationMetaDataIds-java.util.Collection-">setDestinationMetaDataIds(Collection&lt;Number&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Sets which destinations to dispatch the message to.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setDouble-int-double-">setDouble(int, double)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setDouble-java.lang.String-double-">setDouble(String, double)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#setError-java.lang.String-">setError(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Sets the error string to be associated with this response.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setEscapeProcessing-boolean-">setEscapeProcessing(boolean)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setFetchDirection-int-">setFetchDirection(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setFetchSize-int-">setFetchSize(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setFloat-int-float-">setFloat(int, float)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setFloat-java.lang.String-float-">setFloat(String, float)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#setFrom-java.lang.String-">setFrom(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Sets the FROM field to use for dispatched e-mail messages.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#setHost-java.lang.String-">setHost(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Sets the SMTP server address.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#setId-java.lang.String-">setId(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Sets the unique ID for the attachment.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setInt-int-int-">setInt(int, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setInt-java.lang.String-int-">setInt(String, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setKeyColumns-int:A-">setKeyColumns(int[])</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setLong-int-long-">setLong(int, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setLong-java.lang.String-long-">setLong(String, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setMatchColumn-int-">setMatchColumn(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setMatchColumn-int:A-">setMatchColumn(int[])</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setMatchColumn-java.lang.String-">setMatchColumn(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setMatchColumn-java.lang.String:A-">setMatchColumn(String[])</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setMaxFieldSize-int-">setMaxFieldSize(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setMaxRows-int-">setMaxRows(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#setMessage-java.lang.String-">setMessage(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Sets the response data.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setMetaData-javax.sql.RowSetMetaData-">setMetaData(RowSetMetaData)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/AttachmentEntry.html#setMimeType-java.lang.String-">setMimeType(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/AttachmentEntry.html" title="class in com.mirth.connect.userutil">AttachmentEntry</a></dt>
<dd>
<div class="block">Sets the MIME type of the attachment entry.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/AttachmentEntry.html#setName-java.lang.String-">setName(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/AttachmentEntry.html" title="class in com.mirth.connect.userutil">AttachmentEntry</a></dt>
<dd>
<div class="block">Sets the name of the attachment entry.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNCharacterStream-int-java.io.Reader-">setNCharacterStream(int, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNCharacterStream-int-java.io.Reader-long-">setNCharacterStream(int, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNCharacterStream-java.lang.String-java.io.Reader-long-">setNCharacterStream(String, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNCharacterStream-java.lang.String-java.io.Reader-">setNCharacterStream(String, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNClob-java.lang.String-java.sql.NClob-">setNClob(String, NClob)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNClob-java.lang.String-java.io.Reader-long-">setNClob(String, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNClob-java.lang.String-java.io.Reader-">setNClob(String, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNClob-int-java.io.Reader-long-">setNClob(int, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNClob-int-java.sql.NClob-">setNClob(int, NClob)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNClob-int-java.io.Reader-">setNClob(int, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNString-int-java.lang.String-">setNString(int, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNString-java.lang.String-java.lang.String-">setNString(String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNull-int-int-">setNull(int, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNull-java.lang.String-int-">setNull(String, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNull-int-int-java.lang.String-">setNull(int, int, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setNull-java.lang.String-int-java.lang.String-">setNull(String, int, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setObject-int-java.lang.Object-int-int-">setObject(int, Object, int, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setObject-java.lang.String-java.lang.Object-int-int-">setObject(String, Object, int, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setObject-int-java.lang.Object-int-">setObject(int, Object, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setObject-java.lang.String-java.lang.Object-int-">setObject(String, Object, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setObject-java.lang.String-java.lang.Object-">setObject(String, Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setObject-int-java.lang.Object-">setObject(int, Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setOriginalRow--">setOriginalRow()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setPageSize-int-">setPageSize(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setPassword-java.lang.String-">setPassword(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#setPassword-java.lang.String-">setPassword(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Sets the password to use to authenticate to the SMTP server.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#setPort-java.lang.String-">setPort(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Sets the SMTP server port.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setQueryTimeout-int-">setQueryTimeout(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setReadOnly-boolean-">setReadOnly(boolean)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#setRealm-java.lang.String-">setRealm(String)</a></span> - Method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Sets the realm that the request has been authenticated with.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setRef-int-java.sql.Ref-">setRef(int, Ref)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#setResponseHeaders-java.util.Map-">setResponseHeaders(Map&lt;String, List&lt;String&gt;&gt;)</a></span> - Method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Sets the map of HTTP headers to be sent along with the authentication response.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setRowId-int-java.sql.RowId-">setRowId(int, RowId)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setRowId-java.lang.String-java.sql.RowId-">setRowId(String, RowId)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#setSecure-java.lang.String-">setSecure(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Sets the encryption security layer to use for the SMTP connection.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setShort-int-short-">setShort(int, short)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setShort-java.lang.String-short-">setShort(String, short)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setShowDeleted-boolean-">setShowDeleted(boolean)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#setSocketTimeout-int-">setSocketTimeout(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Sets the socket connection timeout value.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/RawMessage.html#setSourceMap-java.util.Map-">setSourceMap(Map&lt;String, Object&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></dt>
<dd>
<div class="block">Sets the source map to be used at the beginning of the channel dispatch.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setSQLXML-int-java.sql.SQLXML-">setSQLXML(int, SQLXML)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setSQLXML-java.lang.String-java.sql.SQLXML-">setSQLXML(String, SQLXML)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#setStatus-com.mirth.connect.plugins.httpauth.userutil.AuthStatus-">setStatus(AuthStatus)</a></span> - Method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Sets the accept/reject status of the authentication attempt.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#setStatus-com.mirth.connect.userutil.Status-">setStatus(Status)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Sets the status of this response.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#setStatusMessage-java.lang.String-">setStatusMessage(String)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Sets the status message to use for this response.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setString-int-java.lang.String-">setString(int, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setString-java.lang.String-java.lang.String-">setString(String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setSyncProvider-java.lang.String-">setSyncProvider(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setTableName-java.lang.String-">setTableName(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setTime-int-java.sql.Time-">setTime(int, Time)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setTime-int-java.sql.Time-java.util.Calendar-">setTime(int, Time, Calendar)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setTime-java.lang.String-java.sql.Time-">setTime(String, Time)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setTime-java.lang.String-java.sql.Time-java.util.Calendar-">setTime(String, Time, Calendar)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setTimestamp-int-java.sql.Timestamp-">setTimestamp(int, Timestamp)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setTimestamp-java.lang.String-java.sql.Timestamp-">setTimestamp(String, Timestamp)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setTimestamp-int-java.sql.Timestamp-java.util.Calendar-">setTimestamp(int, Timestamp, Calendar)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setTimestamp-java.lang.String-java.sql.Timestamp-java.util.Calendar-">setTimestamp(String, Timestamp, Calendar)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setTransactionIsolation-int-">setTransactionIsolation(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/Attachment.html#setType-java.lang.String-">setType(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></dt>
<dd>
<div class="block">Sets the MIME type for the attachment.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setType-int-">setType(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setTypeMap-java.util.Map-">setTypeMap(Map&lt;String, Class&lt;?&gt;&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setUrl-java.lang.String-">setUrl(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setURL-int-java.net.URL-">setURL(int, URL)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#setUseAuthentication-boolean-">setUseAuthentication(boolean)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Sets whether authentication is needed for the SMTP server.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#setUsername-java.lang.String-">setUsername(String)</a></span> - Method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Sets the username that the request has been authenticated with.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#setUsername-java.lang.String-">setUsername(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#setUsername-java.lang.String-">setUsername(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Sets the username to use to authenticate to the SMTP server.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#size--">size()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#size--">size()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#size--">size()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#size--">size()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns the number of elements in this list.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#size--">size()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Returns the number of key-value mappings in this map.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#size--">size()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">SMTPConnection</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Used to send e-mail messages.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#SMTPConnection-java.lang.String-java.lang.String-int-boolean-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">SMTPConnection(String, String, int, boolean, String, String, String, String)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Instantiates an SMTP connection used to send e-mail messages with.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SMTPConnection.html#SMTPConnection-java.lang.String-java.lang.String-boolean-java.lang.String-java.lang.String-java.lang.String-java.lang.String-">SMTPConnection(String, String, boolean, String, String, String, String)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></dt>
<dd>
<div class="block">Instantiates an SMTP connection used to send e-mail messages with.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/SMTPConnectionFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">SMTPConnectionFactory</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Utility class used to create SMTPConnection object using the server's default SMTP settings.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">SourceMap</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#SourceMap-java.util.Map-">SourceMap(Map&lt;String, Object&gt;)</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>
<div class="block">Instantiates a new SourceMap object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#startChannel-java.lang.String-">startChannel(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Start a deployed channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#startConnector-java.lang.String-java.lang.Integer-">startConnector(String, Integer)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Start a connector on a given channel.</div>
</dd>
<dt><a href="com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil"><span class="typeNameLink">Status</span></a> - Enum in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">Denotes the status of a connector message or response.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#stopChannel-java.lang.String-">stopChannel(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Stop a deployed channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#stopConnector-java.lang.String-java.lang.Integer-">stopConnector(String, Integer)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Stop a connector on a given channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#subList-int-int-">subList(int, int)</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns a view of the portion of this list between the specified
 <tt>fromIndex</tt>, inclusive, and <tt>toIndex</tt>, exclusive.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#Success--">Success()</a></span> - Static method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Convenience method to create a new AuthenticationResult with the SUCCESS status.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html#Success-java.lang.String-java.lang.String-">Success(String, String)</a></span> - Static method in class com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthenticationResult.html" title="class in com.mirth.connect.plugins.httpauth.userutil">AuthenticationResult</a></dt>
<dd>
<div class="block">Convenience method to create a new AuthenticationResult with the SUCCESS status.</div>
</dd>
</dl>
<a name="I:T">
<!--   -->
</a>
<h2 class="title">T</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#toArray--">toArray()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns an array containing all of the elements in this list in proper
 sequence (from first to last element).</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#toArray-java.lang.Object:A-">toArray(Object[])</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns an array containing all of the elements in this list in
 proper sequence (from first to last element); the runtime type of
 the returned array is that of the specified array.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#toCollection-java.lang.String-">toCollection(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#toCollection--">toCollection()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#toCollection-int-">toCollection(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/XmlUtil.html#toJson-java.lang.String-">toJson(String)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/XmlUtil.html" title="class in com.mirth.connect.userutil">XmlUtil</a></dt>
<dd>
<div class="block">Converts an XML string to JSON, while stripping bound namespace prefixes.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/XmlUtil.html#toJson-java.lang.String-boolean-">toJson(String, boolean)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/XmlUtil.html" title="class in com.mirth.connect.userutil">XmlUtil</a></dt>
<dd>
<div class="block">Converts an XML string to JSON.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/XmlUtil.html#toJson-java.lang.String-boolean-boolean-boolean-boolean-">toJson(String, boolean, boolean, boolean, boolean)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/XmlUtil.html" title="class in com.mirth.connect.userutil">XmlUtil</a></dt>
<dd>
<div class="block">Converts an XML string to JSON.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/XmlUtil.html#toJson-java.lang.String-boolean-boolean-boolean-boolean-boolean-boolean-">toJson(String, boolean, boolean, boolean, boolean, boolean, boolean)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/XmlUtil.html" title="class in com.mirth.connect.userutil">XmlUtil</a></dt>
<dd>
<div class="block">Converts an XML string to JSON.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DeployedState.html#toString--">toString()</a></span> - Method in enum com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DeployedState.html" title="enum in com.mirth.connect.server.userutil">DeployedState</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ContentType.html#toString--">toString()</a></span> - Method in enum com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ContentType.html" title="enum in com.mirth.connect.userutil">ContentType</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html#toString--">toString()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></dt>
<dd>
<div class="block">Returns a string representation of the object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ImmutableMessage.html#toString--">toString()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></dt>
<dd>
<div class="block">Returns a string representation of the object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ListBuilder.html#toString--">toString()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></dt>
<dd>
<div class="block">Returns a string representation of the object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#toString--">toString()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Returns a string representation of the object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageHeaders.html#toString--">toString()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageHeaders.html" title="class in com.mirth.connect.userutil">MessageHeaders</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MessageParameters.html#toString--">toString()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MessageParameters.html" title="class in com.mirth.connect.userutil">MessageParameters</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Response.html#toString--">toString()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></dt>
<dd>
<div class="block">Returns a string representation of the object.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/JsonUtil.html#toXml-java.lang.String-">toXml(String)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/JsonUtil.html" title="class in com.mirth.connect.userutil">JsonUtil</a></dt>
<dd>
<div class="block">Converts a JSON string to XML.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/JsonUtil.html#toXml-java.lang.String-boolean-boolean-">toXml(String, boolean, boolean)</a></span> - Static method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/JsonUtil.html" title="class in com.mirth.connect.userutil">JsonUtil</a></dt>
<dd>
<div class="block">Converts a JSON string to XML.</div>
</dd>
</dl>
<a name="I:U">
<!--   -->
</a>
<h2 class="title">U</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelUtil.html#undeployChannel-java.lang.String-">undeployChannel(String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></dt>
<dd>
<div class="block">Undeploy a channel.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#undoDelete--">undoDelete()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#undoInsert--">undoInsert()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#undoUpdate--">undoUpdate()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#unsetMatchColumn-int-">unsetMatchColumn(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#unsetMatchColumn-int:A-">unsetMatchColumn(int[])</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#unsetMatchColumn-java.lang.String-">unsetMatchColumn(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#unsetMatchColumn-java.lang.String:A-">unsetMatchColumn(String[])</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#unwrap-java.lang.Class-">unwrap(Class&lt;T&gt;)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateArray-java.lang.String-java.sql.Array-">updateArray(String, Array)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateArray-int-java.sql.Array-">updateArray(int, Array)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateAsciiStream-java.lang.String-java.io.InputStream-int-">updateAsciiStream(String, InputStream, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateAsciiStream-int-java.io.InputStream-int-">updateAsciiStream(int, InputStream, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateAsciiStream-int-java.io.InputStream-long-">updateAsciiStream(int, InputStream, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateAsciiStream-java.lang.String-java.io.InputStream-long-">updateAsciiStream(String, InputStream, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateAsciiStream-int-java.io.InputStream-">updateAsciiStream(int, InputStream)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateAsciiStream-java.lang.String-java.io.InputStream-">updateAsciiStream(String, InputStream)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-java.lang.Object-java.lang.String-">updateAttachment(ImmutableConnectorMessage, String, Object, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Updates an attachment associated with a given connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-java.lang.Object-java.lang.String-boolean-">updateAttachment(ImmutableConnectorMessage, String, Object, String, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Updates an attachment associated with a given connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-com.mirth.connect.server.userutil.Attachment-">updateAttachment(ImmutableConnectorMessage, Attachment)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Updates an attachment associated with a given connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-com.mirth.connect.server.userutil.Attachment-boolean-">updateAttachment(ImmutableConnectorMessage, Attachment, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Updates an attachment associated with a given connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-java.lang.String-java.lang.Long-com.mirth.connect.server.userutil.Attachment-">updateAttachment(String, Long, Attachment)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Updates an attachment associated with a given connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-java.lang.String-java.lang.Long-com.mirth.connect.server.userutil.Attachment-boolean-">updateAttachment(String, Long, Attachment, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Updates an attachment associated with a given connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-java.lang.String-java.lang.Long-java.lang.String-java.lang.Object-java.lang.String-">updateAttachment(String, Long, String, Object, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Updates an attachment associated with a given connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-java.lang.String-java.lang.Long-java.lang.String-java.lang.Object-java.lang.String-boolean-">updateAttachment(String, Long, String, Object, String, boolean)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></dt>
<dd>
<div class="block">Updates an attachment associated with a given connector message.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBigDecimal-java.lang.String-java.math.BigDecimal-">updateBigDecimal(String, BigDecimal)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBigDecimal-int-java.math.BigDecimal-">updateBigDecimal(int, BigDecimal)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBinaryStream-java.lang.String-java.io.InputStream-int-">updateBinaryStream(String, InputStream, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBinaryStream-int-java.io.InputStream-int-">updateBinaryStream(int, InputStream, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBinaryStream-int-java.io.InputStream-long-">updateBinaryStream(int, InputStream, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBinaryStream-java.lang.String-java.io.InputStream-long-">updateBinaryStream(String, InputStream, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBinaryStream-int-java.io.InputStream-">updateBinaryStream(int, InputStream)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBinaryStream-java.lang.String-java.io.InputStream-">updateBinaryStream(String, InputStream)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBlob-java.lang.String-java.sql.Blob-">updateBlob(String, Blob)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBlob-int-java.sql.Blob-">updateBlob(int, Blob)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBlob-int-java.io.InputStream-long-">updateBlob(int, InputStream, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBlob-java.lang.String-java.io.InputStream-long-">updateBlob(String, InputStream, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBlob-int-java.io.InputStream-">updateBlob(int, InputStream)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBlob-java.lang.String-java.io.InputStream-">updateBlob(String, InputStream)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBoolean-java.lang.String-boolean-">updateBoolean(String, boolean)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBoolean-int-boolean-">updateBoolean(int, boolean)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateByte-java.lang.String-byte-">updateByte(String, byte)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateByte-int-byte-">updateByte(int, byte)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBytes-java.lang.String-byte:A-">updateBytes(String, byte[])</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateBytes-int-byte:A-">updateBytes(int, byte[])</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateCharacterStream-java.lang.String-java.io.Reader-int-">updateCharacterStream(String, Reader, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateCharacterStream-int-java.io.Reader-int-">updateCharacterStream(int, Reader, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateCharacterStream-int-java.io.Reader-long-">updateCharacterStream(int, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateCharacterStream-java.lang.String-java.io.Reader-long-">updateCharacterStream(String, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateCharacterStream-int-java.io.Reader-">updateCharacterStream(int, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateCharacterStream-java.lang.String-java.io.Reader-">updateCharacterStream(String, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateClob-java.lang.String-java.sql.Clob-">updateClob(String, Clob)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateClob-int-java.sql.Clob-">updateClob(int, Clob)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateClob-int-java.io.Reader-long-">updateClob(int, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateClob-java.lang.String-java.io.Reader-long-">updateClob(String, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateClob-int-java.io.Reader-">updateClob(int, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateClob-java.lang.String-java.io.Reader-">updateClob(String, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateDate-java.lang.String-java.sql.Date-">updateDate(String, Date)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateDate-int-java.sql.Date-">updateDate(int, Date)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateDouble-java.lang.String-double-">updateDouble(String, double)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateDouble-int-double-">updateDouble(int, double)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateFloat-java.lang.String-float-">updateFloat(String, float)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateFloat-int-float-">updateFloat(int, float)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateInt-java.lang.String-int-">updateInt(String, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateInt-int-int-">updateInt(int, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateLong-java.lang.String-long-">updateLong(String, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateLong-int-long-">updateLong(int, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNCharacterStream-int-java.io.Reader-long-">updateNCharacterStream(int, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNCharacterStream-java.lang.String-java.io.Reader-long-">updateNCharacterStream(String, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNCharacterStream-int-java.io.Reader-">updateNCharacterStream(int, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNCharacterStream-java.lang.String-java.io.Reader-">updateNCharacterStream(String, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNClob-int-java.sql.NClob-">updateNClob(int, NClob)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNClob-java.lang.String-java.sql.NClob-">updateNClob(String, NClob)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNClob-int-java.io.Reader-long-">updateNClob(int, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNClob-java.lang.String-java.io.Reader-long-">updateNClob(String, Reader, long)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNClob-int-java.io.Reader-">updateNClob(int, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNClob-java.lang.String-java.io.Reader-">updateNClob(String, Reader)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNString-int-java.lang.String-">updateNString(int, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNString-java.lang.String-java.lang.String-">updateNString(String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNull-java.lang.String-">updateNull(String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateNull-int-">updateNull(int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateObject-java.lang.String-java.lang.Object-int-">updateObject(String, Object, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateObject-java.lang.String-java.lang.Object-">updateObject(String, Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateObject-int-java.lang.Object-int-">updateObject(int, Object, int)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateObject-int-java.lang.Object-">updateObject(int, Object)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateRef-java.lang.String-java.sql.Ref-">updateRef(String, Ref)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateRef-int-java.sql.Ref-">updateRef(int, Ref)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateRow--">updateRow()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateRowId-int-java.sql.RowId-">updateRowId(int, RowId)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateRowId-java.lang.String-java.sql.RowId-">updateRowId(String, RowId)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateShort-java.lang.String-short-">updateShort(String, short)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateShort-int-short-">updateShort(int, short)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateSQLXML-int-java.sql.SQLXML-">updateSQLXML(int, SQLXML)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateSQLXML-java.lang.String-java.sql.SQLXML-">updateSQLXML(String, SQLXML)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateString-java.lang.String-java.lang.String-">updateString(String, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateString-int-java.lang.String-">updateString(int, String)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateTime-java.lang.String-java.sql.Time-">updateTime(String, Time)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateTime-int-java.sql.Time-">updateTime(int, Time)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateTimestamp-java.lang.String-java.sql.Timestamp-">updateTimestamp(String, Timestamp)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#updateTimestamp-int-java.sql.Timestamp-">updateTimestamp(int, Timestamp)</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/mirth/connect/server/userutil/UUIDGenerator.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">UUIDGenerator</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Utility class to create unique identifiers.</div>
</dd>
</dl>
<a name="I:V">
<!--   -->
</a>
<h2 class="title">V</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html" title="enum in com.mirth.connect.plugins.httpauth.userutil">AuthStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DeployedState.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DeployedState.html" title="enum in com.mirth.connect.server.userutil">DeployedState</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ContentType.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ContentType.html" title="enum in com.mirth.connect.userutil">ContentType</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Status.html#valueOf-java.lang.String-">valueOf(String)</a></span> - Static method in enum com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a></dt>
<dd>
<div class="block">Returns the enum constant of this type with the specified name.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html#values--">values()</a></span> - Static method in enum com.mirth.connect.plugins.httpauth.userutil.<a href="com/mirth/connect/plugins/httpauth/userutil/AuthStatus.html" title="enum in com.mirth.connect.plugins.httpauth.userutil">AuthStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/ChannelMap.html#values--">values()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/DeployedState.html#values--">values()</a></span> - Static method in enum com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/DeployedState.html" title="enum in com.mirth.connect.server.userutil">DeployedState</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/SourceMap.html#values--">values()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ContentType.html#values--">values()</a></span> - Static method in enum com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ContentType.html" title="enum in com.mirth.connect.userutil">ContentType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/MapBuilder.html#values--">values()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></dt>
<dd>
<div class="block">Returns a <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util"><code>Collection</code></a> view of the values contained in this map.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/ResponseMap.html#values--">values()</a></span> - Method in class com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/userutil/Status.html#values--">values()</a></span> - Static method in enum com.mirth.connect.userutil.<a href="com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum type, in
the order they are declared.</div>
</dd>
<dt><a href="com/mirth/connect/server/userutil/VMRouter.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">VMRouter</span></a> - Class in <a href="com/mirth/connect/server/userutil/package-summary.html">com.mirth.connect.server.userutil</a></dt>
<dd>
<div class="block">Utility class used to dispatch messages to channels.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/VMRouter.html#VMRouter--">VMRouter()</a></span> - Constructor for class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/VMRouter.html" title="class in com.mirth.connect.server.userutil">VMRouter</a></dt>
<dd>
<div class="block">Instantiates a VMRouter object.</div>
</dd>
</dl>
<a name="I:W">
<!--   -->
</a>
<h2 class="title">W</h2>
<dl>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html#wasNull--">wasNull()</a></span> - Method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></dt>
<dd>&nbsp;</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/FileUtil.html#write-java.lang.String-boolean-java.lang.String-">write(String, boolean, String)</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/FileUtil.html" title="class in com.mirth.connect.server.userutil">FileUtil</a></dt>
<dd>
<div class="block">Writes a string to a specified file, creating the file if it does not exist.</div>
</dd>
<dt><span class="memberNameLink"><a href="com/mirth/connect/server/userutil/FileUtil.html#write-java.lang.String-boolean-byte:A-">write(String, boolean, byte[])</a></span> - Static method in class com.mirth.connect.server.userutil.<a href="com/mirth/connect/server/userutil/FileUtil.html" title="class in com.mirth.connect.server.userutil">FileUtil</a></dt>
<dd>
<div class="block">Writes a byte array to a file, creating the file if it does not exist.</div>
</dd>
</dl>
<a name="I:X">
<!--   -->
</a>
<h2 class="title">X</h2>
<dl>
<dt><a href="com/mirth/connect/userutil/XmlUtil.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">XmlUtil</span></a> - Class in <a href="com/mirth/connect/userutil/package-summary.html">com.mirth.connect.userutil</a></dt>
<dd>
<div class="block">Provides XML utility methods.</div>
</dd>
</dl>
<a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:J">J</a>&nbsp;<a href="#I:K">K</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a href="#I:W">W</a>&nbsp;<a href="#I:X">X</a>&nbsp;</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="overview-summary.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li class="navBarCell1Rev">Index</li>
<li><a href="help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="index.html?index-all.html" target="_top">Frames</a></li>
<li><a href="index-all.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
