<connectorMetaData path="js">
	<name>JavaScript Writer</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to execute JavaScript to connect to an arbitrary destination.</description>
	<clientClassName>com.mirth.connect.connectors.js.JavaScriptWriter</clientClassName>
	<serverClassName>com.mirth.connect.connectors.js.JavaScriptDispatcher</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.js.JavaScriptDispatcherProperties</sharedClassName>
	<library type="CLIENT" path="js-client.jar" />
	<library type="SHARED" path="js-shared.jar" />
	<library type="SERVER" path="js-server.jar" />
	<transformers></transformers>
	<protocol>js</protocol>
	<type>DESTINATION</type>
</connectorMetaData>
