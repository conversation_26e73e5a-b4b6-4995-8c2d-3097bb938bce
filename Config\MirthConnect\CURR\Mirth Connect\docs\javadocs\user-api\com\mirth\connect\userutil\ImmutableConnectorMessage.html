<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:21 PDT 2023 -->
<title>ImmutableConnectorMessage</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ImmutableConnectorMessage";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":42,"i6":10,"i7":42,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/mirth/connect/userutil/ImmutableAttachment.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/mirth/connect/userutil/ImmutableConnectorMessage.html" target="_top">Frames</a></li>
<li><a href="ImmutableConnectorMessage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.userutil</div>
<h2 title="Class ImmutableConnectorMessage" class="title">Class ImmutableConnectorMessage</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.userutil.ImmutableConnectorMessage</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ImmutableConnectorMessage</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">This class represents a connector message and is used to retrieve details such as the message ID,
 metadata ID, status, and various content types.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#ImmutableConnectorMessage-com.mirth.connect.donkey.model.message.ConnectorMessage-">ImmutableConnectorMessage</a></span>(com.mirth.connect.donkey.model.message.ConnectorMessage&nbsp;connectorMessage)</code>
<div class="block">Instantiates a new ImmutableConnectorMessage object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#ImmutableConnectorMessage-com.mirth.connect.donkey.model.message.ConnectorMessage-boolean-">ImmutableConnectorMessage</a></span>(com.mirth.connect.donkey.model.message.ConnectorMessage&nbsp;connectorMessage,
                         boolean&nbsp;modifiableMaps)</code>
<div class="block">Instantiates a new ImmutableConnectorMessage object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#ImmutableConnectorMessage-com.mirth.connect.donkey.model.message.ConnectorMessage-boolean-java.util.Map-">ImmutableConnectorMessage</a></span>(com.mirth.connect.donkey.model.message.ConnectorMessage&nbsp;connectorMessage,
                         boolean&nbsp;modifiableMaps,
                         <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;destinationIdMap)</code>
<div class="block">Instantiates a new ImmutableConnectorMessage object.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getChannelId--">getChannelId</a></span>()</code>
<div class="block">Returns the ID of the channel associated with this connector message.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getChannelMap--">getChannelMap</a></span>()</code>
<div class="block">Returns the channel map.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getChannelName--">getChannelName</a></span>()</code>
<div class="block">Returns the Name of the channel associated with this connector message.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getConnectorMap--">getConnectorMap</a></span>()</code>
<div class="block">Returns the connector map.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getConnectorName--">getConnectorName</a></span>()</code>
<div class="block">Returns the name of the connector associated with this connector message.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getContent-com.mirth.connect.userutil.ContentType-">getContent</a></span>(<a href="../../../../com/mirth/connect/userutil/ContentType.html" title="enum in com.mirth.connect.userutil">ContentType</a>&nbsp;contentType)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">The getContent(contentType) method has been deprecated and will soon be removed.
             Please use getMessageContent(contentType) instead.</span></div>
</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getDestinationIdMap--">getDestinationIdMap</a></span>()</code>
<div class="block">Returns a Map of destination connector names linked to their corresponding connector metadata
 ID.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getDestinationNameMap--">getDestinationNameMap</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getDestinationIdMap--"><code>getDestinationIdMap()</code></a> instead.</span></div>
</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getEncoded--">getEncoded</a></span>()</code>
<div class="block">Retrieves encoded content associated with this connector message.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getEncodedData--">getEncodedData</a></span>()</code>
<div class="block">Retrieves encoded content associated with this connector message.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getMessageContent-com.mirth.connect.userutil.ContentType-">getMessageContent</a></span>(<a href="../../../../com/mirth/connect/userutil/ContentType.html" title="enum in com.mirth.connect.userutil">ContentType</a>&nbsp;contentType)</code>
<div class="block">Retrieves content associated with this connector message.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getMessageId--">getMessageId</a></span>()</code>
<div class="block">Returns the sequential ID of the overall Message associated with this connector message.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getMetaDataId--">getMetaDataId</a></span>()</code>
<div class="block">Returns the metadata ID of this connector message.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getPostProcessorError--">getPostProcessorError</a></span>()</code>
<div class="block">Returns the postprocessing error string associated with this connector message, if it exists.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getProcessedRaw--">getProcessedRaw</a></span>()</code>
<div class="block">Retrieves processed raw content associated with this connector message.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getProcessedRawData--">getProcessedRawData</a></span>()</code>
<div class="block">Retrieves processed raw content associated with this connector message.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getProcessedResponse--">getProcessedResponse</a></span>()</code>
<div class="block">Retrieves processed response content associated with this connector message.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getProcessedResponseData--">getProcessedResponseData</a></span>()</code>
<div class="block">Retrieves processed response content associated with this connector message.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getProcessingError--">getProcessingError</a></span>()</code>
<div class="block">Returns the processing error string associated with this connector message, if it exists.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getRaw--">getRaw</a></span>()</code>
<div class="block">Retrieves raw content associated with this connector message.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getRawData--">getRawData</a></span>()</code>
<div class="block">Retrieves raw content associated with this connector message.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Calendar.html?is-external=true" title="class or interface in java.util">Calendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getReceivedDate--">getReceivedDate</a></span>()</code>
<div class="block">Returns the date/time that this connector message was created by the channel.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponse--">getResponse</a></span>()</code>
<div class="block">Retrieves response content associated with this connector message.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponseData--">getResponseData</a></span>()</code>
<div class="block">Retrieves response content associated with this connector message.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Calendar.html?is-external=true" title="class or interface in java.util">Calendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponseDate--">getResponseDate</a></span>()</code>
<div class="block">Returns the date/time immediately after this connector message's response is received.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponseError--">getResponseError</a></span>()</code>
<div class="block">Returns the response error string associated with this connector message, if it exists.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponseMap--">getResponseMap</a></span>()</code>
<div class="block">Returns the response map.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponseTransformed--">getResponseTransformed</a></span>()</code>
<div class="block">Retrieves transformed response content associated with this connector message.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getResponseTransformedData--">getResponseTransformedData</a></span>()</code>
<div class="block">Retrieves transformed response content associated with this connector message.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getSendAttempts--">getSendAttempts</a></span>()</code>
<div class="block">Returns the number of times this message has been attempted to be dispatched by the
 connector.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Calendar.html?is-external=true" title="class or interface in java.util">Calendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getSendDate--">getSendDate</a></span>()</code>
<div class="block">Returns the date/time immediately before this connector message's most recent send attempt.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getServerId--">getServerId</a></span>()</code>
<div class="block">Returns the ID of the server associated with this connector message.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getSourceMap--">getSourceMap</a></span>()</code>
<div class="block">Returns the source map.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getStatus--">getStatus</a></span>()</code>
<div class="block">Returns the status (e.g.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getTransformed--">getTransformed</a></span>()</code>
<div class="block">Retrieves transformed content associated with this connector message.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getTransformedData--">getTransformedData</a></span>()</code>
<div class="block">Retrieves transformed content associated with this connector message.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#toString--">toString</a></span>()</code>
<div class="block">Returns a string representation of the object.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ImmutableConnectorMessage-com.mirth.connect.donkey.model.message.ConnectorMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ImmutableConnectorMessage</h4>
<pre>public&nbsp;ImmutableConnectorMessage(com.mirth.connect.donkey.model.message.ConnectorMessage&nbsp;connectorMessage)</pre>
<div class="block">Instantiates a new ImmutableConnectorMessage object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message that this object will reference for retrieving data.</dd>
</dl>
</li>
</ul>
<a name="ImmutableConnectorMessage-com.mirth.connect.donkey.model.message.ConnectorMessage-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ImmutableConnectorMessage</h4>
<pre>public&nbsp;ImmutableConnectorMessage(com.mirth.connect.donkey.model.message.ConnectorMessage&nbsp;connectorMessage,
                                 boolean&nbsp;modifiableMaps)</pre>
<div class="block">Instantiates a new ImmutableConnectorMessage object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message that this object will reference for retrieving data.</dd>
<dd><code>modifiableMaps</code> - If true, variable maps (e.g. connector/channel/response) will be modifiable, and
            values may be set in them as well as retrieved. Otherwise, data will only be able
            to be retrieved from the maps, and no updates will be allowed.</dd>
</dl>
</li>
</ul>
<a name="ImmutableConnectorMessage-com.mirth.connect.donkey.model.message.ConnectorMessage-boolean-java.util.Map-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ImmutableConnectorMessage</h4>
<pre>public&nbsp;ImmutableConnectorMessage(com.mirth.connect.donkey.model.message.ConnectorMessage&nbsp;connectorMessage,
                                 boolean&nbsp;modifiableMaps,
                                 <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;destinationIdMap)</pre>
<div class="block">Instantiates a new ImmutableConnectorMessage object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message that this object will reference for retrieving data.</dd>
<dd><code>modifiableMaps</code> - If true, variable maps (e.g. connector/channel/response) will be modifiable, and
            values may be set in them as well as retrieved. Otherwise, data will only be able
            to be retrieved from the maps, and no updates will be allowed.</dd>
<dd><code>destinationIdMap</code> - A map containing all applicable destination names in the channel and their
            corresponding connector metadata ids.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getMetaDataId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMetaDataId</h4>
<pre>public&nbsp;int&nbsp;getMetaDataId()</pre>
<div class="block">Returns the metadata ID of this connector message. Note that the source connector has a
 metadata ID of 0.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The metadata ID of this connector message.</dd>
</dl>
</li>
</ul>
<a name="getChannelId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChannelId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getChannelId()</pre>
<div class="block">Returns the ID of the channel associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The ID of the channel associated with this connector message.</dd>
</dl>
</li>
</ul>
<a name="getChannelName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChannelName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getChannelName()</pre>
<div class="block">Returns the Name of the channel associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Name of the channel associated with this connector message.</dd>
</dl>
</li>
</ul>
<a name="getConnectorName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConnectorName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getConnectorName()</pre>
<div class="block">Returns the name of the connector associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The name of the connector associated with this connector message.</dd>
</dl>
</li>
</ul>
<a name="getServerId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getServerId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getServerId()</pre>
<div class="block">Returns the ID of the server associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The ID of the server associated with this connector message.</dd>
</dl>
</li>
</ul>
<a name="getReceivedDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReceivedDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Calendar.html?is-external=true" title="class or interface in java.util">Calendar</a>&nbsp;getReceivedDate()</pre>
<div class="block">Returns the date/time that this connector message was created by the channel.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The date/time that this connector message was created by the channel.</dd>
</dl>
</li>
</ul>
<a name="getSendAttempts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSendAttempts</h4>
<pre>public&nbsp;int&nbsp;getSendAttempts()</pre>
<div class="block">Returns the number of times this message has been attempted to be dispatched by the
 connector.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The number of times this message has been attempted to be dispatched by the
         connector.</dd>
</dl>
</li>
</ul>
<a name="getSendDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSendDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Calendar.html?is-external=true" title="class or interface in java.util">Calendar</a>&nbsp;getSendDate()</pre>
<div class="block">Returns the date/time immediately before this connector message's most recent send attempt.
 Only valid for destination connectors in the response transformer or postprocessor. Returns
 null otherwise.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The date/time immediately before this connector message's most recent send attempt.</dd>
</dl>
</li>
</ul>
<a name="getResponseDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponseDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Calendar.html?is-external=true" title="class or interface in java.util">Calendar</a>&nbsp;getResponseDate()</pre>
<div class="block">Returns the date/time immediately after this connector message's response is received. Only
 valid for destination connectors in the response transformer or postprocessor. Returns null
 otherwise.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The date/time immediately after this connector message's response is received.</dd>
</dl>
</li>
</ul>
<a name="getStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatus</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a>&nbsp;getStatus()</pre>
<div class="block">Returns the status (e.g. SENT) of this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The status (e.g. SENT) of this connector message.</dd>
</dl>
</li>
</ul>
<a name="getMessageContent-com.mirth.connect.userutil.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageContent</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a>&nbsp;getMessageContent(<a href="../../../../com/mirth/connect/userutil/ContentType.html" title="enum in com.mirth.connect.userutil">ContentType</a>&nbsp;contentType)</pre>
<div class="block">Retrieves content associated with this connector message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>contentType</code> - The ContentType (e.g. RAW, ENCODED) of the content to retrieve.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The content, as an ImmutableMessageContent object.</dd>
</dl>
</li>
</ul>
<a name="getContent-com.mirth.connect.userutil.ContentType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getContent</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public&nbsp;<a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a>&nbsp;getContent(<a href="../../../../com/mirth/connect/userutil/ContentType.html" title="enum in com.mirth.connect.userutil">ContentType</a>&nbsp;contentType)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">The getContent(contentType) method has been deprecated and will soon be removed.
             Please use getMessageContent(contentType) instead.</span></div>
<div class="block">Retrieves content associated with this connector message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>contentType</code> - The ContentType (e.g. RAW, ENCODED) of the content to retrieve.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The content, as an ImmutableMessageContent object.</dd>
</dl>
</li>
</ul>
<a name="getRaw--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRaw</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a>&nbsp;getRaw()</pre>
<div class="block">Retrieves raw content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The raw content, as an ImmutableMessageContent object.</dd>
</dl>
</li>
</ul>
<a name="getRawData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRawData</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRawData()</pre>
<div class="block">Retrieves raw content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The raw content, as a string.</dd>
</dl>
</li>
</ul>
<a name="getProcessedRaw--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessedRaw</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a>&nbsp;getProcessedRaw()</pre>
<div class="block">Retrieves processed raw content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The processed raw content, as an ImmutableMessageContent object.</dd>
</dl>
</li>
</ul>
<a name="getProcessedRawData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessedRawData</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessedRawData()</pre>
<div class="block">Retrieves processed raw content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The processed raw content, as a string.</dd>
</dl>
</li>
</ul>
<a name="getTransformed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTransformed</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a>&nbsp;getTransformed()</pre>
<div class="block">Retrieves transformed content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The transformed content, as an ImmutableMessageContent object.</dd>
</dl>
</li>
</ul>
<a name="getTransformedData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTransformedData</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTransformedData()</pre>
<div class="block">Retrieves transformed content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The transformed content, as a string.</dd>
</dl>
</li>
</ul>
<a name="getEncoded--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEncoded</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a>&nbsp;getEncoded()</pre>
<div class="block">Retrieves encoded content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The encoded content, as an ImmutableMessageContent object.</dd>
</dl>
</li>
</ul>
<a name="getEncodedData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEncodedData</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getEncodedData()</pre>
<div class="block">Retrieves encoded content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The encoded content, as a string.</dd>
</dl>
</li>
</ul>
<a name="getResponse--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponse</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a>&nbsp;getResponse()</pre>
<div class="block">Retrieves response content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The response content, as an ImmutableMessageContent object.</dd>
</dl>
</li>
</ul>
<a name="getResponseData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponseData</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a>&nbsp;getResponseData()</pre>
<div class="block">Retrieves response content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The response content, as a string.</dd>
</dl>
</li>
</ul>
<a name="getResponseTransformed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponseTransformed</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a>&nbsp;getResponseTransformed()</pre>
<div class="block">Retrieves transformed response content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The transformed response content, as an ImmutableMessageContent object.</dd>
</dl>
</li>
</ul>
<a name="getResponseTransformedData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponseTransformedData</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResponseTransformedData()</pre>
<div class="block">Retrieves transformed response content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The transformed response content, as a string.</dd>
</dl>
</li>
</ul>
<a name="getProcessedResponse--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessedResponse</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a>&nbsp;getProcessedResponse()</pre>
<div class="block">Retrieves processed response content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The processed response content, as an ImmutableMessageContent object.</dd>
</dl>
</li>
</ul>
<a name="getProcessedResponseData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessedResponseData</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a>&nbsp;getProcessedResponseData()</pre>
<div class="block">Retrieves processed response content associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The processed response content, as a string.</dd>
</dl>
</li>
</ul>
<a name="getMessageId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageId</h4>
<pre>public&nbsp;long&nbsp;getMessageId()</pre>
<div class="block">Returns the sequential ID of the overall Message associated with this connector message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The sequential ID of the overall Message associated with this connector message.</dd>
</dl>
</li>
</ul>
<a name="getSourceMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourceMap</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getSourceMap()</pre>
<div class="block">Returns the source map. This map is unmodifiable and only data retrieval will be allowed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The source map.</dd>
</dl>
</li>
</ul>
<a name="getConnectorMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConnectorMap</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getConnectorMap()</pre>
<div class="block">Returns the connector map. If this connector message was instantiated with a 'true' value for
 modifiableMaps, then this map will allow both data retrieval and updates. Otherwise, the map
 will be unmodifiable and only data retrieval will be allowed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The connector map.</dd>
</dl>
</li>
</ul>
<a name="getChannelMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChannelMap</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getChannelMap()</pre>
<div class="block">Returns the channel map. If this connector message was instantiated with a 'true' value for
 modifiableMaps, then this map will allow both data retrieval and updates. Otherwise, the map
 will be unmodifiable and only data retrieval will be allowed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The channel map.</dd>
</dl>
</li>
</ul>
<a name="getResponseMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponseMap</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getResponseMap()</pre>
<div class="block">Returns the response map. If this connector message was instantiated with a 'true' value for
 modifiableMaps, then this map will allow both data retrieval and updates. Otherwise, the map
 will be unmodifiable and only data retrieval will be allowed. In addition, if this connector
 message was instantiated with the destinationNameMap parameter, the map will check
 destination names as well as the proper "d#" keys when retrieving data.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The response map.</dd>
</dl>
</li>
</ul>
<a name="getPostProcessorError--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPostProcessorError</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPostProcessorError()</pre>
<div class="block">Returns the postprocessing error string associated with this connector message, if it exists.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The postprocessing error string associated with this connector message, if it exists.</dd>
</dl>
</li>
</ul>
<a name="getProcessingError--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProcessingError</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProcessingError()</pre>
<div class="block">Returns the processing error string associated with this connector message, if it exists.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The processing error string associated with this connector message, if it exists.</dd>
</dl>
</li>
</ul>
<a name="getResponseError--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResponseError</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResponseError()</pre>
<div class="block">Returns the response error string associated with this connector message, if it exists.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The response error string associated with this connector message, if it exists.</dd>
</dl>
</li>
</ul>
<a name="getDestinationNameMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinationNameMap</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getDestinationNameMap()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html#getDestinationIdMap--"><code>getDestinationIdMap()</code></a> instead.</span></div>
<div class="block">Returns a Map of destination connector names linked to their corresponding "d#" response map
 keys (where "#" is the destination connector metadata ID).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A Map of destination connector names linked to their corresponding "d#" response map
         keys.</dd>
</dl>
</li>
</ul>
<a name="getDestinationIdMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinationIdMap</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;getDestinationIdMap()</pre>
<div class="block">Returns a Map of destination connector names linked to their corresponding connector metadata
 ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A Map of destination connector names linked to their corresponding connector metadata
         ID.</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<div class="block">Returns a string representation of the object. In general, the
 <code>toString</code> method returns a string that
 "textually represents" this object. The result should
 be a concise but informative representation that is easy for a
 person to read.
 It is recommended that all subclasses override this method.
 <p>
 The <code>toString</code> method for class <code>Object</code>
 returns a string consisting of the name of the class of which the
 object is an instance, the at-sign character `<code>@</code>', and
 the unsigned hexadecimal representation of the hash code of the
 object. In other words, this method returns a string equal to the
 value of:
 <blockquote>
 <pre>
 getClass().getName() + '@' + Integer.toHexString(hashCode())
 </pre></blockquote></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a string representation of the object.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/mirth/connect/userutil/ImmutableAttachment.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/mirth/connect/userutil/ImmutableConnectorMessage.html" target="_top">Frames</a></li>
<li><a href="ImmutableConnectorMessage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
