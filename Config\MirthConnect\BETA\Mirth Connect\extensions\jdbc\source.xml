<connectorMetaData path="jdbc">
	<name>Database Reader</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to poll any supported JDBC-compatabile database for data. Rows are returned as XML and JavaScript can be used for advanced logic.</description>
	<clientClassName>com.mirth.connect.connectors.jdbc.DatabaseReader</clientClassName>
	<serverClassName>com.mirth.connect.connectors.jdbc.DatabaseReceiver</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.jdbc.DatabaseReceiverProperties</sharedClassName>
	<library type="CLIENT" path="jdbc-client.jar" />
	<library type="SHARED" path="jdbc-shared.jar" />
	<library type="SERVER" path="jdbc-server.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.connectors.jdbc.DatabaseConnectorServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.connectors.jdbc.DatabaseConnectorServlet"/>
	<protocol>jdbc</protocol>
	<type>SOURCE</type>
</connectorMetaData>
