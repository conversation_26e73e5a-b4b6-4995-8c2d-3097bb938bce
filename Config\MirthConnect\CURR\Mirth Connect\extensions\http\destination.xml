<connectorMetaData path="http">
	<name>HTTP Sender</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to send messages to an HTTP server. Custom header properties can be specified.</description>
	<clientClassName>com.mirth.connect.connectors.http.HttpSender</clientClassName>
	<serverClassName>com.mirth.connect.connectors.http.HttpDispatcher</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.http.HttpDispatcherProperties</sharedClassName>
	<templateClassName>com.mirth.connect.connectors.http.HttpSenderCodeTemplatePlugin</templateClassName>
	<library type="CLIENT" path="http-client.jar" />
	<library type="SHARED" path="http-shared.jar" />
	<library type="SERVER" path="http-server.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.connectors.http.HttpConnectorServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.connectors.http.HttpConnectorServlet"/>
	<transformers></transformers>
	<protocol>http</protocol>
	<type>DESTINATION</type>
</connectorMetaData>
