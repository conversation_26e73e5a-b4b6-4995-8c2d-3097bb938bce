<connectorMetaData path="doc">
	<name>Document Writer</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to create RTF or PDF documents based on an HTML template. PDF files can be password protected.</description>
	<clientClassName>com.mirth.connect.connectors.doc.DocumentWriter</clientClassName>
	<serverClassName>com.mirth.connect.connectors.doc.DocumentDispatcher</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.doc.DocumentDispatcherProperties</sharedClassName>
	<library type="CLIENT" path="doc-client.jar" />
	<library type="SHARED" path="doc-shared.jar" />
	<library type="SERVER" path="doc-server.jar" />
	<library type="SERVER" path="lib/itext-2.1.7.jar" />
	<library type="SERVER" path="lib/itext-rtf-2.1.7.jar" />
	<library type="SERVER" path="lib/flying-saucer-core-9.0.1.jar" />
	<library type="SERVER" path="lib/flying-saucer-pdf-9.0.1.jar" />
	<library type="SERVER" path="lib/openhtmltopdf-core-1.0.9.jar" />
	<library type="SERVER" path="lib/openhtmltopdf-pdfbox-1.0.9.jar" />
	<library type="SERVER" path="lib/xmpbox-2.0.24.jar" />
	<library type="SERVER" path="lib/fontbox-2.0.24.jar" />
	<library type="SERVER" path="lib/graphics2d-0.32.jar" />
	<library type="SERVER" path="lib/pdfbox-2.0.24.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.connectors.doc.DocumentConnectorServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.connectors.doc.DocumentConnectorServlet"/>
	<transformers></transformers>
	<protocol>doc</protocol>
	<type>DESTINATION</type>
</connectorMetaData>
