# sample properties to initialize log4j
rootLogger = ERROR,stdout,fout

# stdout appender
appender.console.type = Console
appender.console.name = stdout
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = %-5p %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c: %m%n

# file appender
dir.logs = C:\\\\Mobile Aspects\\\\MirthConnect\\\\CURR\\\\Mirth Connect\\\\logs
appender.rolling.type = RollingFile
appender.rolling.name = fout
appender.rolling.fileName = logs/mirth.log
appender.rolling.filePattern = logs/mirth.log.%i
appender.rolling.policies.type = Policies
appender.rolling.policies.size.type = SizeBasedTriggeringPolicy
appender.rolling.policies.size.size = 500KB
appender.rolling.strategy.type = DefaultRolloverStrategy
appender.rolling.strategy.max = 20
appender.rolling.layout.type = PatternLayout
appender.rolling.layout.pattern = %-5p %d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %c: %m%n

# splash screen
logger.mirth.name = com.mirth.connect.server.Mirth
logger.mirth.level = INFO

# Mirth Connect server logging
logger.donkeyEngineController.name = com.mirth.connect.server.controllers.DonkeyEngineController
logger.donkeyEngineController.level = INFO
logger.recoveryTask.name = com.mirth.connect.donkey.server.channel.RecoveryTask
logger.recoveryTask.level = INFO
logger.fileReceiver.name = com.mirth.connect.connectors.file.FileReceiver
logger.fileReceiver.level = WARN

# Mirth Connect channel logging
logger.transformer.name = transformer
logger.transformer.level = DEBUG
logger.preprocessor.name = preprocessor
logger.preprocessor.level = DEBUG
logger.postprocessor.name = postprocessor
logger.postprocessor.level = DEBUG
logger.deploy.name = deploy
logger.deploy.level = DEBUG
logger.undeploy.name = undeploy
logger.undeploy.level = DEBUG
logger.filter.name = filter
logger.filter.level = DEBUG
logger.db-connector.name = db-connector
logger.db-connector.level = DEBUG
logger.js-connector.name = js-connector
logger.js-connector.level = DEBUG
logger.attachment.name = attachment
logger.attachment.level = DEBUG
logger.batch.name = batch
logger.batch.level = DEBUG
logger.response.name = response
logger.response.level = DEBUG
logger.shutdown.name = shutdown
logger.shutdown.level = DEBUG

# SQL Logging
logger.sql.name = java.sql
logger.sql.level = ERROR
