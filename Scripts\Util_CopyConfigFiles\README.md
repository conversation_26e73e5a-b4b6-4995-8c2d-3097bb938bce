# Configuration Files Copy Utility

## Overview

This PowerShell script (`copy-config-files.ps1`) is designed to extract and backup configuration files from the Mobile Aspects workspace to a centralized Config folder while preserving the original directory structure.

## Purpose

The script helps maintain a backup of all configuration and settings files across the entire Mobile Aspects project, making it easier to:
- Track configuration changes
- Backup important settings
- Restore configurations when needed
- Maintain consistency across environments

## Features

- **Recursive scanning**: Searches the entire workspace for configuration files
- **Pattern-based detection**: Uses predefined patterns to identify configuration files
- **Directory structure preservation**: Maintains the original folder hierarchy in the backup
- **Smart exclusions**: Automatically excludes temporary files, library files, and the Config folder itself
- **User-controlled overwrites**: Prompts for confirmation when files already exist
- **Batch operations**: Supports "Yes to All" and "Skip All" options for efficiency
- **Detailed reporting**: Provides comprehensive feedback on the copy process

## Usage

### Basic Usage
```powershell
.\copy-config-files.ps1
```

### With Custom Paths
```powershell
.\copy-config-files.ps1 -SourceRoot "." -ConfigRoot "./Config"
```

### Parameters

| Parameter | Default Value | Description |
|-----------|---------------|-------------|
| `SourceRoot` | `"."` | Source directory to scan (relative to Mobile Aspects root) |
| `ConfigRoot` | `"./Config"` | Destination directory for configuration backups |

## Configuration File Patterns

The script automatically detects files with the following patterns:

### General Configuration Files
- `*.orig` - Original configuration backups
- `*.properties` - Java properties files
- `*.config` - General configuration files
- `*.ini` - Windows INI files
- `*.conf` - Unix/Linux configuration files
- `*.cfg` - Configuration files
- `*.env` - Environment variable files
- `*.toml` - TOML configuration files

### JSON Configuration Files
- `*settings.json` - Settings files
- `appsettings*.json` - ASP.NET Core application settings
- `libman.json` - Library Manager configuration
- `*.deps.json` - .NET dependency files
- `*.runtimeconfig.json` - .NET runtime configuration

### XML Configuration Files
- `*.xml` - XML configuration files
- `web.config` - ASP.NET web configuration
- `app.config` - .NET application configuration

### YAML Configuration Files
- `*.yml` - YAML files
- `*.yaml` - YAML files

## Exclusion Patterns

The script automatically excludes files from the following directories:
- `*\temp\*` - Temporary directories
- `*\appdata\temp\*` - Application data temp folders
- `*\dir.appdata\temp\*` - Directory application data temp
- `*\wwwroot\assets\libs\*` - Web asset libraries
- `*\wwwroot\assets\lang\*` - Language files
- `*\wwwroot\assets\Icons\*` - Icon files
- `*\legal\*` - Legal documents
- `*\.install4j\*` - Install4J files
- `*\javachecker\*` - Java checker files
- `*\mcadministrator\*` - MC Administrator files
- `*\openjfx\*` - OpenJFX files
- Files already in the Config directory

## Interactive Options

When the script encounters existing files, it provides the following options:

| Option | Description |
|--------|-------------|
| **Y** | Yes - Overwrite this specific file |
| **N** | No - Skip this specific file (default) |
| **A** | Yes to All - Overwrite all remaining conflicting files |
| **S** | Skip All - Skip all remaining conflicting files |

## Output and Reporting

The script provides detailed feedback including:
- Number of files found and filtered
- Real-time copy/skip/overwrite status
- Final summary with counts
- Directory-based grouping of copied files
- Color-coded messages for easy reading

### Color Coding
- **Green**: Successful operations, completion messages
- **Yellow**: Informational messages, file counts
- **Red**: Error messages
- **Gray**: Skipped files
- **Cyan**: Section headers

## Example Output

```
Starting configuration file copy process...
Source Root: .
Config Root: ./Config
Created Config directory: ./Config
Found 45 potential configuration files
After filtering: 32 configuration files to copy

File already exists: WebApp\appsettings.json
Do you want to overwrite it? (Y/N/A for Yes to All/S to Skip All): A
Overwritten: WebApp\appsettings.json
Copied: Services\service.config
Copied: Database\connection.properties

Copy process completed!
Files copied: 30
Files skipped: 2

Summary of copied configuration files by directory:
  WebApp: 5 files
  Services: 8 files
  Database: 3 files
```

## Best Practices

1. **Run regularly**: Execute the script periodically to keep backups current
2. **Review changes**: Check the Config folder for any unexpected changes
3. **Use version control**: Consider adding the Config folder to your version control system
4. **Test configurations**: Verify that backed-up configurations are valid
5. **Document changes**: Keep notes about significant configuration modifications

## Troubleshooting

### Common Issues

**Script doesn't find files**: Ensure you're running from the correct directory and have proper permissions.

**Permission errors**: Run PowerShell as Administrator if encountering access denied errors.

**Path too long errors**: Windows has a 260-character path limit; consider shortening folder names if needed.

**Execution policy errors**: You may need to set the execution policy:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## Version History

- **2025-07-05**: Updated to use relative paths, enhanced Config folder exclusion, added user confirmation for overwrites
- **2025-07-04**: Initial version with basic copy functionality

## Author

Augment Agent - Mobile Aspects Configuration Management Utility
