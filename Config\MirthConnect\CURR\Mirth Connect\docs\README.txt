MIRTH CONNECT README

Contents
==================================================

1. General Information
2. Installation and Upgrade
3. Starting Mirth Connect
4. Running Mirth Connect in Java 9 or greater
5. More Information

1. General Information
==================================================

Mirth Connect is an open source health care integration engine. By supporting numerous standards (like HL7, EDI/X12, XML, NCPDP, DICOM, and Delimited Text) and protocols (like TCP/LLP, HTTP, JDBC, and File/FTP/SFTP), Mirth Connect allows for the filtering, transformation, and routing of messages between disparate systems to allow them to share data.


2. Installation and Upgrade
==================================================

Mirth Connect installers are available for individual operating systems (EXE for Windows, RPM and SH for Linux, and DMG for Mac OS X). Pre-packaged distributions are also available for individual operating systems (ZIP for Windows, TAR.GZ for Linux, and TAR.GZ for Mac OS X). The installer allows you to automatically upgrade previous Mirth Connect installations (starting with version 1.5).

Mirth Connect installers also come with the option to install and start a service which will run the background. You also have the option of installing and running the Mirth Connect Server Manager, which allows you to start and stop the service on some operating systems, change Mirth Connect properties and backend database settings, and view the server logs.

An optional Mirth Connect Command Line Interface can be installed, allowing you to connect to a running Mirth Connect Server using a command line. This tool is useful for performing or scripting server tasks without opening the Mirth Connect Administrator.

After the installation, the Mirth Connect directory layout will look as follows:

/appdata/mirthdb: The embedded database (Do NOT delete if you specify Derby as your database). This will be created when the Mirth Connect Server is started. The path for appdata is defined by the dir.appdata property in mirth.properties.
/cli-lib: Libraries for the Mirth Connect Command Line Interface (if installed)
/client-lib: Libraries for the Mirth Connect Administrator
/conf: Configuration files
/custom-lib: Place your custom user libraries here. These libraries will be loaded on the Mirth Connect Server classpath when it is started, making them accessible to channel scripts.
/docs: This document and a copy of the Mirth Connect license
/docs/javadocs: Generated javadocs for the installed version of Mirth Connect. These documents are also available when the server is running at http://[server address]:8080/javadocs/ (i.e. http://localhost:8080/javadocs/).
/extensions: Libraries and meta data for Plug-ins and Connectors
/logs: Default location for logs generated by Mirth Connect and its sub-components
/manager-lib: Libraries for the Mirth Connect Server Manager (if installed)
/public_html: Directory exposed by the embedded web server
/server-lib: Mirth Connect server libraries
/webapps: Directory exposed by the embedded web server to host webapps


3. Starting Mirth Connect
==================================================

Once Mirth Connect has been installed, there are several ways to connect to launch the Mirth Connect Administrator. On a Windows installation, there is a Mirth Connect Administrator item in the Start Menu which launches the application directly.

If the option is not available, you can connect to the Mirth Connect Administrator launch page which by default should be available at http://[server address]:8080 (i.e. http://localhost:8080). Clicking the Launch Mirth Connect Administrator button will connect you to the server which will be listening on https://[server address]:8443 (i.e. https://localhost:8443). If running a new installation, the default username and password for the login screen is admin and admin. This should be changed immediately for security purposes.

If you are launching the administrator for the first time, you will notice that the libraries for the Mirth Connect Administrator will be loaded. This feature allows you run the Administrator from any remote Mirth Connect server without having to download and install a separate client.

You may also notice a security warning when starting the administrator (dialog box depends on browser being used). This is normal and you should click Run to continue launching the administrator.


4. Running Mirth Connect in Java 9 or greater
==================================================

When running Mirth Connect in Java 9+, there are separate JVM options that need to be set.

As of version 4.4, these options will be automatically included in the Java command string as needed, when running mcserver, mcservice, or mccommand.

If you are running Mirth Connect manually with your own Java command string, and you are using Java 9+, then make sure to include the options from /docs/mcservice-java9+.vmoptions.


5. More Information
==================================================

You can find the latest Mirth Connect release information, documentation, and more at: https://www.nextgen.com/products-and-services/integration-engine

Follow the Mirth Connect Open Source Community at: https://github.com/nextgenhealthcare/connect