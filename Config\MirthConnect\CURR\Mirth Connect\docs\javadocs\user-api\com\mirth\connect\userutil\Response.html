<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:21 PDT 2023 -->
<title>Response</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Response";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/mirth/connect/userutil/MessageParameters.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/mirth/connect/userutil/Response.html" target="_top">Frames</a></li>
<li><a href="Response.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.userutil</div>
<h2 title="Class Response" class="title">Class Response</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.userutil.Response</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Response</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">This class represents a channel or destination response and is used to retrieve details such as
 the response data, message status, and errors.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#Response--">Response</a></span>()</code>
<div class="block">Instantiates a new Response object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#Response-com.mirth.connect.donkey.model.message.Response-">Response</a></span>(com.mirth.connect.donkey.model.message.Response&nbsp;response)</code>
<div class="block">Instantiates a new Response object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#Response-com.mirth.connect.userutil.Status-java.lang.String-">Response</a></span>(<a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a>&nbsp;status,
        <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</code>
<div class="block">Instantiates a new Response object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#Response-com.mirth.connect.userutil.Status-java.lang.String-java.lang.String-">Response</a></span>(<a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a>&nbsp;status,
        <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
        <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;statusMessage)</code>
<div class="block">Instantiates a new Response object.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#Response-com.mirth.connect.userutil.Status-java.lang.String-java.lang.String-java.lang.String-">Response</a></span>(<a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a>&nbsp;status,
        <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
        <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;statusMessage,
        <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;error)</code>
<div class="block">Instantiates a new Response object.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#Response-java.lang.String-">Response</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</code>
<div class="block">Instantiates a new Response object.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#equals-java.lang.Object-">equals</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;other)</code>
<div class="block">Indicates whether some other object is "equal to" this one.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#getError--">getError</a></span>()</code>
<div class="block">Returns the error string associated with this response, if it exists.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#getMessage--">getMessage</a></span>()</code>
<div class="block">Returns the actual response data, as a string.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#getStatus--">getStatus</a></span>()</code>
<div class="block">Returns the Status (e.g.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#getStatusMessage--">getStatusMessage</a></span>()</code>
<div class="block">Returns a brief message explaining the reason for the current status.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#setError-java.lang.String-">setError</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;error)</code>
<div class="block">Sets the error string to be associated with this response.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#setMessage-java.lang.String-">setMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</code>
<div class="block">Sets the response data.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#setStatus-com.mirth.connect.userutil.Status-">setStatus</a></span>(<a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a>&nbsp;status)</code>
<div class="block">Sets the status of this response.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#setStatusMessage-java.lang.String-">setStatusMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;statusMessage)</code>
<div class="block">Sets the status message to use for this response.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/Response.html#toString--">toString</a></span>()</code>
<div class="block">Returns a string representation of the object.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Response--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Response</h4>
<pre>public&nbsp;Response()</pre>
<div class="block">Instantiates a new Response object.</div>
</li>
</ul>
<a name="Response-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Response</h4>
<pre>public&nbsp;Response(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</pre>
<div class="block">Instantiates a new Response object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - The actual response data.</dd>
</dl>
</li>
</ul>
<a name="Response-com.mirth.connect.userutil.Status-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Response</h4>
<pre>public&nbsp;Response(<a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a>&nbsp;status,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</pre>
<div class="block">Instantiates a new Response object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>status</code> - The status (e.g. SENT, ERROR) of the response.</dd>
<dd><code>message</code> - The actual response data.</dd>
</dl>
</li>
</ul>
<a name="Response-com.mirth.connect.userutil.Status-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Response</h4>
<pre>public&nbsp;Response(<a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a>&nbsp;status,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;statusMessage)</pre>
<div class="block">Instantiates a new Response object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>status</code> - The status (e.g. SENT, ERROR) of the response.</dd>
<dd><code>message</code> - The actual response data.</dd>
<dd><code>statusMessage</code> - A brief message explaining the reason for the current status.</dd>
</dl>
</li>
</ul>
<a name="Response-com.mirth.connect.userutil.Status-java.lang.String-java.lang.String-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>Response</h4>
<pre>public&nbsp;Response(<a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a>&nbsp;status,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;statusMessage,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;error)</pre>
<div class="block">Instantiates a new Response object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>status</code> - The status (e.g. SENT, ERROR) of the response.</dd>
<dd><code>message</code> - The actual response data.</dd>
<dd><code>statusMessage</code> - A brief message explaining the reason for the current status.</dd>
<dd><code>error</code> - The error string associated with this response, if applicable.</dd>
</dl>
</li>
</ul>
<a name="Response-com.mirth.connect.donkey.model.message.Response-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Response</h4>
<pre>public&nbsp;Response(com.mirth.connect.donkey.model.message.Response&nbsp;response)</pre>
<div class="block">Instantiates a new Response object.
 
 NOTE: This should be excluded from the public Javadoc.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>response</code> - The underlying Donkey Response object to reference.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getMessage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessage</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMessage()</pre>
<div class="block">Returns the actual response data, as a string.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The actual response data, as a string.</dd>
</dl>
</li>
</ul>
<a name="setMessage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMessage</h4>
<pre>public&nbsp;void&nbsp;setMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;message)</pre>
<div class="block">Sets the response data.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - The response data (String) to use.</dd>
</dl>
</li>
</ul>
<a name="getStatus--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatus</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a>&nbsp;getStatus()</pre>
<div class="block">Returns the Status (e.g. SENT, QUEUED) of this response.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The Status (e.g. SENT, QUEUED) of this response.</dd>
</dl>
</li>
</ul>
<a name="setStatus-com.mirth.connect.userutil.Status-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStatus</h4>
<pre>public&nbsp;void&nbsp;setStatus(<a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a>&nbsp;status)</pre>
<div class="block">Sets the status of this response.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>status</code> - The status (e.g. SENT, QUEUED) to use for this response.</dd>
</dl>
</li>
</ul>
<a name="getError--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getError</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getError()</pre>
<div class="block">Returns the error string associated with this response, if it exists.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The error string associated with this response, if it exists.</dd>
</dl>
</li>
</ul>
<a name="setError-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setError</h4>
<pre>public&nbsp;void&nbsp;setError(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;error)</pre>
<div class="block">Sets the error string to be associated with this response.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>error</code> - The error string to use.</dd>
</dl>
</li>
</ul>
<a name="getStatusMessage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatusMessage</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getStatusMessage()</pre>
<div class="block">Returns a brief message explaining the reason for the current status.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A brief message explaining the reason for the current status.</dd>
</dl>
</li>
</ul>
<a name="setStatusMessage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStatusMessage</h4>
<pre>public&nbsp;void&nbsp;setStatusMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;statusMessage)</pre>
<div class="block">Sets the status message to use for this response.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>statusMessage</code> - A brief message explaining the reason for the current status.</dd>
</dl>
</li>
</ul>
<a name="equals-java.lang.Object-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>equals</h4>
<pre>public&nbsp;boolean&nbsp;equals(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;other)</pre>
<div class="block">Indicates whether some other object is "equal to" this one.
 <p>
 The <code>equals</code> method implements an equivalence relation
 on non-null object references:
 <ul>
 <li>It is <i>reflexive</i>: for any non-null reference value
     <code>x</code>, <code>x.equals(x)</code> should return
     <code>true</code>.
 <li>It is <i>symmetric</i>: for any non-null reference values
     <code>x</code> and <code>y</code>, <code>x.equals(y)</code>
     should return <code>true</code> if and only if
     <code>y.equals(x)</code> returns <code>true</code>.
 <li>It is <i>transitive</i>: for any non-null reference values
     <code>x</code>, <code>y</code>, and <code>z</code>, if
     <code>x.equals(y)</code> returns <code>true</code> and
     <code>y.equals(z)</code> returns <code>true</code>, then
     <code>x.equals(z)</code> should return <code>true</code>.
 <li>It is <i>consistent</i>: for any non-null reference values
     <code>x</code> and <code>y</code>, multiple invocations of
     <code>x.equals(y)</code> consistently return <code>true</code>
     or consistently return <code>false</code>, provided no
     information used in <code>equals</code> comparisons on the
     objects is modified.
 <li>For any non-null reference value <code>x</code>,
     <code>x.equals(null)</code> should return <code>false</code>.
 </ul>
 <p>
 The <code>equals</code> method for class <code>Object</code> implements
 the most discriminating possible equivalence relation on objects;
 that is, for any non-null reference values <code>x</code> and
 <code>y</code>, this method returns <code>true</code> if and only
 if <code>x</code> and <code>y</code> refer to the same object
 (<code>x == y</code> has the value <code>true</code>).
 <p>
 Note that it is generally necessary to override the <code>hashCode</code>
 method whenever this method is overridden, so as to maintain the
 general contract for the <code>hashCode</code> method, which states
 that equal objects must have equal hash codes.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>other</code> - the reference object with which to compare.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if this object is the same as the obj
          argument; <code>false</code> otherwise.</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang"><code>Object.hashCode()</code></a>, 
<a href="https://docs.oracle.com/javase/8/docs/api/java/util/HashMap.html?is-external=true" title="class or interface in java.util"><code>HashMap</code></a></dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<div class="block">Returns a string representation of the object. In general, the
 <code>toString</code> method returns a string that
 "textually represents" this object. The result should
 be a concise but informative representation that is easy for a
 person to read.
 It is recommended that all subclasses override this method.
 <p>
 The <code>toString</code> method for class <code>Object</code>
 returns a string consisting of the name of the class of which the
 object is an instance, the at-sign character `<code>@</code>', and
 the unsigned hexadecimal representation of the hash code of the
 object. In other words, this method returns a string equal to the
 value of:
 <blockquote>
 <pre>
 getClass().getName() + '@' + Integer.toHexString(hashCode())
 </pre></blockquote></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a string representation of the object.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/mirth/connect/userutil/MessageParameters.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/mirth/connect/userutil/Response.html" target="_top">Frames</a></li>
<li><a href="Response.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
