<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:17 PDT 2023 -->
<title>AttachmentUtil</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AttachmentUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/AttachmentUtil.html" target="_top">Frames</a></li>
<li><a href="AttachmentUtil.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.server.userutil</div>
<h2 title="Class AttachmentUtil" class="title">Class AttachmentUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.server.userutil.AttachmentUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">AttachmentUtil</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Provides utility methods for creating, retrieving, and re-attaching message attachments.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#addAttachment-java.util.List-java.lang.Object-java.lang.String-">addAttachment</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;&nbsp;attachments,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</code>
<div class="block">Creates an Attachment and adds it to the provided list.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#addAttachment-java.util.List-java.lang.Object-java.lang.String-boolean-">addAttachment</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;&nbsp;attachments,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type,
             boolean&nbsp;base64Encode)</code>
<div class="block">Creates an Attachment and adds it to the provided list.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#createAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.Object-java.lang.String-">createAttachment</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</code>
<div class="block">Creates an attachment associated with a given connector message, and inserts it into the
 database.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#createAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.Object-java.lang.String-boolean-">createAttachment</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type,
                boolean&nbsp;base64Encode)</code>
<div class="block">Creates an attachment associated with a given connector message, and inserts it into the
 database.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-">getMessageAttachment</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId)</code>
<div class="block">Retrieves an attachment from the current channel/message ID.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-boolean-">getMessageAttachment</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId,
                    boolean&nbsp;base64Decode)</code>
<div class="block">Retrieves an attachment from the current channel/message ID.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachment-java.lang.String-java.lang.Long-java.lang.String-">getMessageAttachment</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId)</code>
<div class="block">Retrieves an attachment from a specific channel/message ID.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachment-java.lang.String-java.lang.Long-java.lang.String-boolean-">getMessageAttachment</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId,
                    boolean&nbsp;base64Decode)</code>
<div class="block">Retrieves an attachment from a specific channel/message ID.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachmentIds-com.mirth.connect.userutil.ImmutableConnectorMessage-">getMessageAttachmentIds</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</code>
<div class="block">Returns a List of attachment IDs associated with the current channel / message.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachmentIds-java.lang.String-java.lang.Long-">getMessageAttachmentIds</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId)</code>
<div class="block">Returns a List of attachment IDs associated with the current channel / message.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachments-com.mirth.connect.userutil.ImmutableConnectorMessage-">getMessageAttachments</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</code>
<div class="block">Retrieves all attachments associated with a connector message.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachments-com.mirth.connect.userutil.ImmutableConnectorMessage-boolean-">getMessageAttachments</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                     boolean&nbsp;base64Decode)</code>
<div class="block">Retrieves all attachments associated with a connector message.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachments-java.lang.String-java.lang.Long-">getMessageAttachments</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId)</code>
<div class="block">Retrieves all attachments associated with a specific channel/message ID.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachments-java.lang.String-java.lang.Long-boolean-">getMessageAttachments</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                     boolean&nbsp;base64Decode)</code>
<div class="block">Retrieves all attachments associated with a specific channel/message ID.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachmentsFromSourceChannel-com.mirth.connect.userutil.ImmutableConnectorMessage-">getMessageAttachmentsFromSourceChannel</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</code>
<div class="block">Retrieves an attachment from an upstream channel that sent a message to the current channel.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#getMessageAttachmentsFromSourceChannel-com.mirth.connect.userutil.ImmutableConnectorMessage-boolean-">getMessageAttachmentsFromSourceChannel</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                      boolean&nbsp;base64Decode)</code>
<div class="block">Retrieves an attachment from an upstream channel that sent a message to the current channel.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#reAttachMessage-com.mirth.connect.userutil.ImmutableConnectorMessage-">reAttachMessage</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</code>
<div class="block">Replaces any unique attachment tokens (e.g.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#reAttachMessage-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-">reAttachMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;raw,
               <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</code>
<div class="block">Replaces any unique attachment tokens (e.g.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#reAttachMessage-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-boolean-">reAttachMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;raw,
               <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;charsetEncoding,
               boolean&nbsp;binary)</code>
<div class="block">Replaces any unique attachment tokens (e.g.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#reAttachMessage-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-boolean-boolean-boolean-">reAttachMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;raw,
               <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;charsetEncoding,
               boolean&nbsp;binary,
               boolean&nbsp;reattach,
               boolean&nbsp;localOnly)</code>
<div class="block">Replaces any unique attachment tokens (e.g.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-com.mirth.connect.server.userutil.Attachment-">updateAttachment</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;attachment)</code>
<div class="block">Updates an attachment associated with a given connector message.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-com.mirth.connect.server.userutil.Attachment-boolean-">updateAttachment</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;attachment,
                boolean&nbsp;base64Encode)</code>
<div class="block">Updates an attachment associated with a given connector message.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-java.lang.Object-java.lang.String-">updateAttachment</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</code>
<div class="block">Updates an attachment associated with a given connector message.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-java.lang.Object-java.lang.String-boolean-">updateAttachment</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type,
                boolean&nbsp;base64Encode)</code>
<div class="block">Updates an attachment associated with a given connector message.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-java.lang.String-java.lang.Long-com.mirth.connect.server.userutil.Attachment-">updateAttachment</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;attachment)</code>
<div class="block">Updates an attachment associated with a given connector message.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-java.lang.String-java.lang.Long-com.mirth.connect.server.userutil.Attachment-boolean-">updateAttachment</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;attachment,
                boolean&nbsp;base64Encode)</code>
<div class="block">Updates an attachment associated with a given connector message.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-java.lang.String-java.lang.Long-java.lang.String-java.lang.Object-java.lang.String-">updateAttachment</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)</code>
<div class="block">Updates an attachment associated with a given connector message.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html#updateAttachment-java.lang.String-java.lang.Long-java.lang.String-java.lang.Object-java.lang.String-boolean-">updateAttachment</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type,
                boolean&nbsp;base64Encode)</code>
<div class="block">Updates an attachment associated with a given connector message.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="reAttachMessage-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reAttachMessage</h4>
<pre>public static&nbsp;byte[]&nbsp;reAttachMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;raw,
                                     <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;charsetEncoding,
                                     boolean&nbsp;binary)</pre>
<div class="block">Replaces any unique attachment tokens (e.g. "${ATTACH:id}") with the corresponding attachment
 content, and returns the full post-replacement message as a byte array.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>raw</code> - The raw message string to replace tokens from.</dd>
<dd><code>connectorMessage</code> - The ConnectorMessage associated with this message, used to identify the
            channel/message ID.</dd>
<dd><code>charsetEncoding</code> - If binary mode is not used, the resulting byte array will be encoded using this
            charset.</dd>
<dd><code>binary</code> - If enabled, the raw data is assumed to be Base64 encoded. The resulting byte array
            will be the raw Base64 decoded bytes.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The resulting message as a byte array, with all applicable attachment content
         re-inserted.</dd>
</dl>
</li>
</ul>
<a name="reAttachMessage-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-boolean-boolean-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reAttachMessage</h4>
<pre>public static&nbsp;byte[]&nbsp;reAttachMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;raw,
                                     <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;charsetEncoding,
                                     boolean&nbsp;binary,
                                     boolean&nbsp;reattach,
                                     boolean&nbsp;localOnly)</pre>
<div class="block">Replaces any unique attachment tokens (e.g. "${ATTACH:id}") with the corresponding attachment
 content, and returns the full post-replacement message as a byte array.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>raw</code> - The raw message string to replace tokens from.</dd>
<dd><code>connectorMessage</code> - The ConnectorMessage associated with this message, used to identify the
            channel/message ID.</dd>
<dd><code>charsetEncoding</code> - If binary mode is not used, the resulting byte array will be encoded using this
            charset.</dd>
<dd><code>binary</code> - If enabled, the raw data is assumed to be Base64 encoded. The resulting byte array
            will be the raw Base64 decoded bytes.</dd>
<dd><code>reattach</code> - If true, attachment tokens will be replaced with the actual attachment content.
            Otherwise, local attachment tokens will be replaced only with the corresponding
            expanded tokens.</dd>
<dd><code>localOnly</code> - If true, only local attachment tokens will be replaced, and expanded tokens will
            be ignored.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The resulting message as a byte array, with all applicable attachment content
         re-inserted.</dd>
</dl>
</li>
</ul>
<a name="reAttachMessage-com.mirth.connect.userutil.ImmutableConnectorMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reAttachMessage</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;reAttachMessage(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</pre>
<div class="block">Replaces any unique attachment tokens (e.g. "${ATTACH:id}") with the corresponding attachment
 content, and returns the full post-replacement message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The ConnectorMessage associated with this message, used to identify the
            channel/message ID. The message string will be either the encoded or raw content.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The resulting message with all applicable attachment content re-inserted.</dd>
</dl>
</li>
</ul>
<a name="reAttachMessage-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reAttachMessage</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;reAttachMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;raw,
                                     <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</pre>
<div class="block">Replaces any unique attachment tokens (e.g. "${ATTACH:id}") with the corresponding attachment
 content, and returns the full post-replacement message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>raw</code> - The raw message string to replace tokens from.</dd>
<dd><code>connectorMessage</code> - The ConnectorMessage associated with this message, used to identify the
            channel/message ID.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The resulting message with all applicable attachment content re-inserted.</dd>
</dl>
</li>
</ul>
<a name="getMessageAttachmentIds-com.mirth.connect.userutil.ImmutableConnectorMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageAttachmentIds</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getMessageAttachmentIds(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)
                                            throws com.mirth.connect.donkey.model.message.MessageSerializerException</pre>
<div class="block">Returns a List of attachment IDs associated with the current channel / message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The ConnectorMessage associated with this message, used to identify the
            channel/message ID.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A List of attachment IDs associated with the current channel / message.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If the attachment IDs could be retrieved.</dd>
</dl>
</li>
</ul>
<a name="getMessageAttachmentIds-java.lang.String-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageAttachmentIds</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getMessageAttachmentIds(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                                                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId)
                                            throws com.mirth.connect.donkey.model.message.MessageSerializerException</pre>
<div class="block">Returns a List of attachment IDs associated with the current channel / message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The ID of the channel the attachments are associated with.</dd>
<dd><code>messageId</code> - The ID of the message the attachments are associated with.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A List of attachment IDs associated with the current channel / message.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If the attachment IDs could be retrieved.</dd>
</dl>
</li>
</ul>
<a name="getMessageAttachments-com.mirth.connect.userutil.ImmutableConnectorMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageAttachments</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;&nbsp;getMessageAttachments(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)
                                              throws com.mirth.connect.donkey.model.message.MessageSerializerException</pre>
<div class="block">Retrieves all attachments associated with a connector message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The ConnectorMessage associated with this message, used to identify the
            channel/message ID.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A list of attachments associated with the connector message.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If the attachments could not be retrieved.</dd>
</dl>
</li>
</ul>
<a name="getMessageAttachments-com.mirth.connect.userutil.ImmutableConnectorMessage-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageAttachments</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;&nbsp;getMessageAttachments(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                                     boolean&nbsp;base64Decode)
                                              throws com.mirth.connect.donkey.model.message.MessageSerializerException</pre>
<div class="block">Retrieves all attachments associated with a connector message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The ConnectorMessage associated with this message, used to identify the
            channel/message ID.</dd>
<dd><code>base64Decode</code> - If true, the content of each attachment will first be Base64 decoded for
            convenient use.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A list of attachments associated with the connector message.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If the attachments could not be retrieved.</dd>
</dl>
</li>
</ul>
<a name="getMessageAttachments-java.lang.String-java.lang.Long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageAttachments</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;&nbsp;getMessageAttachments(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                                                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId)
                                              throws com.mirth.connect.donkey.model.message.MessageSerializerException</pre>
<div class="block">Retrieves all attachments associated with a specific channel/message ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The ID of the channel to retrieve the attachments from.</dd>
<dd><code>messageId</code> - The ID of the message to retrieve the attachments from.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A list of attachments associated with the channel/message ID.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If the attachments could not be retrieved.</dd>
</dl>
</li>
</ul>
<a name="getMessageAttachments-java.lang.String-java.lang.Long-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageAttachments</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;&nbsp;getMessageAttachments(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                                                     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                                                     boolean&nbsp;base64Decode)
                                              throws com.mirth.connect.donkey.model.message.MessageSerializerException</pre>
<div class="block">Retrieves all attachments associated with a specific channel/message ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The ID of the channel to retrieve the attachments from.</dd>
<dd><code>messageId</code> - The ID of the message to retrieve the attachments from.</dd>
<dd><code>base64Decode</code> - If true, the content of each attachment will first be Base64 decoded for
            convenient use.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A list of attachments associated with the channel/message ID.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If the attachments could not be retrieved.</dd>
</dl>
</li>
</ul>
<a name="getMessageAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;getMessageAttachment(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId)
                                       throws com.mirth.connect.donkey.model.message.MessageSerializerException</pre>
<div class="block">Retrieves an attachment from the current channel/message ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The ConnectorMessage associated with this message, used to identify the
            channel/message ID.</dd>
<dd><code>attachmentId</code> - The ID of the attachment to retrieve.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment associated with the given IDs, or null if none was found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If the attachment could not be retrieved.</dd>
</dl>
</li>
</ul>
<a name="getMessageAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;getMessageAttachment(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId,
                                              boolean&nbsp;base64Decode)
                                       throws com.mirth.connect.donkey.model.message.MessageSerializerException</pre>
<div class="block">Retrieves an attachment from the current channel/message ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The ConnectorMessage associated with this message, used to identify the
            channel/message ID.</dd>
<dd><code>attachmentId</code> - The ID of the attachment to retrieve.</dd>
<dd><code>base64Decode</code> - If true, the content of each attachment will first be Base64 decoded for
            convenient use.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment associated with the given IDs, or null if none was found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If the attachment could not be retrieved.</dd>
</dl>
</li>
</ul>
<a name="getMessageAttachment-java.lang.String-java.lang.Long-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;getMessageAttachment(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId)
                                       throws com.mirth.connect.donkey.model.message.MessageSerializerException</pre>
<div class="block">Retrieves an attachment from a specific channel/message ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The ID of the channel to retrieve the attachment from.</dd>
<dd><code>messageId</code> - The ID of the message to retrieve the attachment from.</dd>
<dd><code>attachmentId</code> - The ID of the attachment to retrieve.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment associated with the given IDs, or null if none was found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If the attachment could not be retrieved.</dd>
</dl>
</li>
</ul>
<a name="getMessageAttachment-java.lang.String-java.lang.Long-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;getMessageAttachment(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId,
                                              boolean&nbsp;base64Decode)
                                       throws com.mirth.connect.donkey.model.message.MessageSerializerException</pre>
<div class="block">Retrieves an attachment from a specific channel/message ID.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The ID of the channel to retrieve the attachment from.</dd>
<dd><code>messageId</code> - The ID of the message to retrieve the attachment from.</dd>
<dd><code>attachmentId</code> - The ID of the attachment to retrieve.</dd>
<dd><code>base64Decode</code> - If true, the content of each attachment will first be Base64 decoded for
            convenient use.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment associated with the given IDs, or null if none was found.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If the attachment could not be retrieved.</dd>
</dl>
</li>
</ul>
<a name="getMessageAttachmentsFromSourceChannel-com.mirth.connect.userutil.ImmutableConnectorMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageAttachmentsFromSourceChannel</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;&nbsp;getMessageAttachmentsFromSourceChannel(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)
                                                               throws com.mirth.connect.donkey.model.message.MessageSerializerException</pre>
<div class="block">Retrieves an attachment from an upstream channel that sent a message to the current channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The ConnectorMessage associated with this message. The channel ID and message ID
            will be retrieved from the source map.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A list of attachments associated with the source channel/message IDs.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If the attachments could not be retrieved.</dd>
</dl>
</li>
</ul>
<a name="getMessageAttachmentsFromSourceChannel-com.mirth.connect.userutil.ImmutableConnectorMessage-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageAttachmentsFromSourceChannel</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;&nbsp;getMessageAttachmentsFromSourceChannel(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                                                      boolean&nbsp;base64Decode)
                                                               throws com.mirth.connect.donkey.model.message.MessageSerializerException</pre>
<div class="block">Retrieves an attachment from an upstream channel that sent a message to the current channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The ConnectorMessage associated with this message. The channel ID and message ID
            will be retrieved from the source map.</dd>
<dd><code>base64Decode</code> - If true, the content of each attachment will first be Base64 decoded for
            convenient use.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A list of attachments associated with the source channel/message IDs.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If the attachments could not be retrieved.</dd>
</dl>
</li>
</ul>
<a name="addAttachment-java.util.List-java.lang.Object-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;addAttachment(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;&nbsp;attachments,
                                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)
                                throws com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</pre>
<div class="block">Creates an Attachment and adds it to the provided list.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>attachments</code> - The list of attachments to add to.</dd>
<dd><code>content</code> - The attachment content (must be a string or byte array).</dd>
<dd><code>type</code> - The MIME type of the attachment.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment added to the list.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</code> - If the attachment content is not a String or byte array.</dd>
</dl>
</li>
</ul>
<a name="addAttachment-java.util.List-java.lang.Object-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;addAttachment(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;&nbsp;attachments,
                                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                                       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type,
                                       boolean&nbsp;base64Encode)
                                throws com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</pre>
<div class="block">Creates an Attachment and adds it to the provided list.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>attachments</code> - The list of attachments to add to.</dd>
<dd><code>content</code> - The attachment content (must be a string or byte array).</dd>
<dd><code>type</code> - The MIME type of the attachment.</dd>
<dd><code>base64Encode</code> - If true, the content of each attachment will first be Base64 encoded for
            convenience.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment added to the list.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</code> - If the attachment content is not a String or byte array.</dd>
</dl>
</li>
</ul>
<a name="createAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.Object-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;createAttachment(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)
                                   throws com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</pre>
<div class="block">Creates an attachment associated with a given connector message, and inserts it into the
 database.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message to be associated with the attachment.</dd>
<dd><code>content</code> - The attachment content (must be a string or byte array).</dd>
<dd><code>type</code> - The MIME type of the attachment.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment that was created and inserted.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</code> - If the attachment content is not a String or byte array.</dd>
</dl>
</li>
</ul>
<a name="createAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.Object-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;createAttachment(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type,
                                          boolean&nbsp;base64Encode)
                                   throws com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</pre>
<div class="block">Creates an attachment associated with a given connector message, and inserts it into the
 database.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message to be associated with the attachment.</dd>
<dd><code>content</code> - The attachment content (must be a string or byte array).</dd>
<dd><code>type</code> - The MIME type of the attachment.</dd>
<dd><code>base64Encode</code> - If true, the content of each attachment will first be Base64 encoded for
            convenience.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment that was created and inserted.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</code> - If the attachment content is not a String or byte array.</dd>
</dl>
</li>
</ul>
<a name="updateAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-java.lang.Object-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;updateAttachment(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)
                                   throws com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</pre>
<div class="block">Updates an attachment associated with a given connector message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message to be associated with the attachment.</dd>
<dd><code>attachmentId</code> - The unique ID of the attachment to update.</dd>
<dd><code>content</code> - The attachment content (must be a string or byte array).</dd>
<dd><code>type</code> - The MIME type of the attachment.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment that was updated.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</code> - If the attachment content is not a String or byte array.</dd>
</dl>
</li>
</ul>
<a name="updateAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-java.lang.String-java.lang.Object-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;updateAttachment(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type,
                                          boolean&nbsp;base64Encode)
                                   throws com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</pre>
<div class="block">Updates an attachment associated with a given connector message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message to be associated with the attachment.</dd>
<dd><code>attachmentId</code> - The unique ID of the attachment to update.</dd>
<dd><code>content</code> - The attachment content (must be a string or byte array).</dd>
<dd><code>type</code> - The MIME type of the attachment.</dd>
<dd><code>base64Encode</code> - If true, the content of each attachment will first be Base64 encoded for
            convenience.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment that was updated.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</code> - If the attachment content is not a String or byte array.</dd>
</dl>
</li>
</ul>
<a name="updateAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-com.mirth.connect.server.userutil.Attachment-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;updateAttachment(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                          <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;attachment)
                                   throws com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</pre>
<div class="block">Updates an attachment associated with a given connector message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message to be associated with the attachment.</dd>
<dd><code>attachment</code> - The Attachment object to update.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment that was updated.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</code> - If the attachment content is not a String or byte array.</dd>
</dl>
</li>
</ul>
<a name="updateAttachment-com.mirth.connect.userutil.ImmutableConnectorMessage-com.mirth.connect.server.userutil.Attachment-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;updateAttachment(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                          <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;attachment,
                                          boolean&nbsp;base64Encode)
                                   throws com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</pre>
<div class="block">Updates an attachment associated with a given connector message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message to be associated with the attachment.</dd>
<dd><code>attachment</code> - The Attachment object to update.</dd>
<dd><code>base64Encode</code> - If true, the content of each attachment will first be Base64 encoded for
            convenience.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment that was updated.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</code> - If the attachment content is not a String or byte array.</dd>
</dl>
</li>
</ul>
<a name="updateAttachment-java.lang.String-java.lang.Long-com.mirth.connect.server.userutil.Attachment-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;updateAttachment(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                                          <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;attachment)
                                   throws com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</pre>
<div class="block">Updates an attachment associated with a given connector message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The ID of the channel the attachment is associated with.</dd>
<dd><code>messageId</code> - The ID of the message the attachment is associated with.</dd>
<dd><code>attachment</code> - The Attachment object to update.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment that was updated.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</code> - If the attachment content is not a String or byte array.</dd>
</dl>
</li>
</ul>
<a name="updateAttachment-java.lang.String-java.lang.Long-com.mirth.connect.server.userutil.Attachment-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;updateAttachment(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                                          <a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;attachment,
                                          boolean&nbsp;base64Encode)
                                   throws com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</pre>
<div class="block">Updates an attachment associated with a given connector message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The ID of the channel the attachment is associated with.</dd>
<dd><code>messageId</code> - The ID of the message the attachment is associated with.</dd>
<dd><code>attachment</code> - The Attachment object to update.</dd>
<dd><code>base64Encode</code> - If true, the content of each attachment will first be Base64 encoded for
            convenience.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment that was updated.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</code> - If the attachment content is not a String or byte array.</dd>
</dl>
</li>
</ul>
<a name="updateAttachment-java.lang.String-java.lang.Long-java.lang.String-java.lang.Object-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>updateAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;updateAttachment(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type)
                                   throws com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</pre>
<div class="block">Updates an attachment associated with a given connector message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The ID of the channel the attachment is associated with.</dd>
<dd><code>messageId</code> - The ID of the message the attachment is associated with.</dd>
<dd><code>attachmentId</code> - The unique ID of the attachment to update.</dd>
<dd><code>content</code> - The attachment content (must be a string or byte array).</dd>
<dd><code>type</code> - The MIME type of the attachment.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment that was updated.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</code> - If the attachment content is not a String or byte array.</dd>
</dl>
</li>
</ul>
<a name="updateAttachment-java.lang.String-java.lang.Long-java.lang.String-java.lang.Object-java.lang.String-boolean-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>updateAttachment</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&nbsp;updateAttachment(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;messageId,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;attachmentId,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;content,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;type,
                                          boolean&nbsp;base64Encode)
                                   throws com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</pre>
<div class="block">Updates an attachment associated with a given connector message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The ID of the channel the attachment is associated with.</dd>
<dd><code>messageId</code> - The ID of the message the attachment is associated with.</dd>
<dd><code>attachmentId</code> - The unique ID of the attachment to update.</dd>
<dd><code>content</code> - The attachment content (must be a string or byte array).</dd>
<dd><code>type</code> - The MIME type of the attachment.</dd>
<dd><code>base64Encode</code> - If true, the content of each attachment will first be Base64 encoded for
            convenience.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The attachment that was updated.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.server.controllers.UnsupportedDataTypeException</code> - If the attachment content is not a String or byte array.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/AttachmentUtil.html" target="_top">Frames</a></li>
<li><a href="AttachmentUtil.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
