version: '3'

services:
  postgres:
    container_name: kc_db
    image: postgres:latest
    environment:
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: password
    ports:
      - 5432:5432

  kc1:
    container_name: kc1
    #image: quay.io/keycloak/keycloak:latest
    image: quay.io/keycloak/keycloak:17.0.0-legacy
    environment:
      DB_VENDOR: postgres
      DB_ADDR: postgres
      KEYCLOAK_FRONTEND_URL: "http://localhost:8000/auth/"
      KEYCLOAK_USER: admin
      KEYCLOAK_PASSWORD: admin
      PROXY_ADDRESS_FORWARDING: "true"
      CACHE_OWNERS_COUNT: 2
      CACHE_OWNERS_AUTH_SESSIONS_COUNT: 2
      JGROUPS_DISCOVERY_PROTOCOL: JDBC_PING
      JGROUPS_DISCOVERY_PROPERTIES: "datasource_jndi_name=java:jboss/datasources/KeycloakDS,initialize_sql=\"CREATE TABLE IF NOT EXISTS JGROUPSPING (own_addr varchar(200) NOT NULL, cluster_name varchar(200) NOT NULL, created TIMESTAMP DEFAULT CURRENT_TIMESTAMP, ping_data BYTEA, constraint PK_JGROUPSPING PRIMARY KEY (own_addr, cluster_name))\",remove_all_data_on_view_change=true"
    depends_on:
      - postgres

  kc2:
    container_name: kc2
    #image: quay.io/keycloak/keycloak:latest
    image: quay.io/keycloak/keycloak:17.0.0-legacy
    environment:
      DB_VENDOR: postgres
      DB_ADDR: postgres
      KEYCLOAK_FRONTEND_URL: "http://localhost:8000/auth/"
      PROXY_ADDRESS_FORWARDING: "true"
      CACHE_OWNERS_COUNT: 2
      CACHE_OWNERS_AUTH_SESSIONS_COUNT: 2
      JGROUPS_DISCOVERY_PROTOCOL: JDBC_PING
      JGROUPS_DISCOVERY_PROPERTIES: "datasource_jndi_name=java:jboss/datasources/KeycloakDS,initialize_sql=\"CREATE TABLE IF NOT EXISTS JGROUPSPING (own_addr varchar(200) NOT NULL, cluster_name varchar(200) NOT NULL, created TIMESTAMP DEFAULT CURRENT_TIMESTAMP, ping_data BYTEA, constraint PK_JGROUPSPING PRIMARY KEY (own_addr, cluster_name))\",remove_all_data_on_view_change=true"
    depends_on:
      - postgres

  lb:
    container_name: kc_lb
    image: nginx:alpine
    volumes:
      - "./nginx.conf:/etc/nginx/conf.d/default.conf"
    ports:
      - "8000:8000"
    depends_on:
      - kc1
      - kc2

