# Configuration File Copy Utilities

## Overview

This directory contains utilities for copying configuration files from application directories to a centralized Config folder while preserving directory structure. This enables centralized configuration management, version control, and easier backup/restore operations.

## Why Use Centralized Configuration?

1. **Centralized Management**: All configuration files in one location
2. **Version Control**: Easy to track configuration changes across applications
3. **Backup and Restore**: Simplified configuration backup and restore
4. **Environment Management**: Different configs for DEV/TEST/PROD environments
5. **Deployment**: Configuration changes independent of application updates
6. **Consistency**: Standardized configuration structure across all applications

## Files in This Directory

### `Util_CopyConfigFiles.ps1`
Main PowerShell script that:
- Copies all configuration files from source directories to Config folder
- Preserves directory structure during copy operations
- Filters configuration files by pattern (*.properties, *.xml, *.json, etc.)
- Excludes temporary files and library files
- Uses relative paths for portability
- Provides interactive options for handling existing files

### `MirthConnect_SwitchOver/`
Specialized utilities for MirthConnect application:
- `MirthConnect_SwitchOver.ps1` - Copies runtime files needed for MirthConnect
- `README.md` - Comprehensive guide for MirthConnect configuration switchover

### `README.md`
This documentation file explaining the utilities and usage instructions.

## Configuration File Patterns

The script automatically identifies and copies files matching these patterns:

### Configuration Files
- `*.orig` - Original configuration backups
- `*.properties` - Java properties files
- `*.config` - .NET configuration files
- `*settings.json` - Application settings files
- `*.xml` - XML configuration files
- `*.yml`, `*.yaml` - YAML configuration files
- `*.ini` - INI configuration files
- `*.conf` - Configuration files
- `*.cfg` - Configuration files
- `*.env` - Environment files
- `*.toml` - TOML configuration files

### Application-Specific Files
- `appsettings*.json` - .NET Core application settings
- `web.config` - ASP.NET web configuration
- `app.config` - .NET application configuration
- `libman.json` - Library manager configuration
- `*.deps.json` - .NET dependency files
- `*.runtimeconfig.json` - .NET runtime configuration

## Excluded Files and Directories

The script automatically excludes:
- `*\temp\*` - Temporary files
- `*\appdata\temp\*` - Application temporary data
- `*\dir.appdata\temp\*` - Directory application temporary data
- `*\wwwroot\assets\libs\*` - Web library files
- `*\wwwroot\assets\lang\*` - Language files
- `*\wwwroot\assets\Icons\*` - Icon files
- `*\legal\*` - Legal documents
- `*\.install4j\*` - Install4J files
- `*\javachecker\*` - Java checker files
- `*\mcadministrator\*` - MirthConnect administrator files
- `*\openjfx\*` - OpenJFX files

## Usage Instructions

### Basic Usage

**Step 1:** Navigate to the script directory:
```powershell
cd "C:\Mobile Aspects\Script\Util_CopyConfigFiles"
```

**Step 2:** Run the configuration copy script:
```powershell
.\Util_CopyConfigFiles.ps1
```

### Advanced Usage with Parameters

**Custom source and destination:**
```powershell
.\Util_CopyConfigFiles.ps1 -SourceRoot "..\..\MyApp" -ConfigRoot "..\..\Config\MyApp"
```

**Using relative paths from script location:**
```powershell
# Copy from Mobile Aspects root to Config folder
.\Util_CopyConfigFiles.ps1 -SourceRoot "../.." -ConfigRoot "../../Config"
```

### Interactive Options

When the script encounters existing files, it provides these options:
- **Y** - Overwrite this file
- **N** - Skip this file (default)
- **A** - Overwrite all remaining files
- **S** - Skip all remaining files

## Directory Structure

### Source Structure (Example)
```
Mobile Aspects\
├── MirthConnect\
│   ├── CURR\
│   │   └── Mirth Connect\
│   │       ├── conf\
│   │       │   ├── mirth.properties
│   │       │   └── log4j2.properties
│   │       └── appdata\
│   └── BETA\
├── Keycloak\
│   └── CURR\
│       └── conf\
│           └── keycloak.conf
└── Script\
    └── Util_CopyConfigFiles\
        └── Util_CopyConfigFiles.ps1
```

### Destination Structure (After Copy)
```
Config\
├── MirthConnect\
│   ├── CURR\
│   │   └── Mirth Connect\
│   │       ├── conf\
│   │       │   ├── mirth.properties
│   │       │   └── log4j2.properties
│   │       └── appdata\
│   └── BETA\
├── Keycloak\
│   └── CURR\
│       └── conf\
│           └── keycloak.conf
└── .vscode\
    └── settings.json
```

## Verification Steps

### 1. Check Files Copied
```powershell
# View summary of copied files
Get-ChildItem "../../Config" -Recurse -File | Group-Object Extension | Sort-Object Name
```

### 2. Verify Directory Structure
```powershell
# Check that directory structure is preserved
Get-ChildItem "../../Config" -Recurse -Directory | Select-Object FullName
```

### 3. Compare File Counts
```powershell
# Compare original vs copied file counts
$originalCount = (Get-ChildItem "../.." -Recurse -Include "*.properties","*.xml","*.json" | Measure-Object).Count
$copiedCount = (Get-ChildItem "../../Config" -Recurse -Include "*.properties","*.xml","*.json" | Measure-Object).Count
Write-Host "Original: $originalCount, Copied: $copiedCount"
```

## How to Switch Back to Original Configuration

If you need applications to use their original configuration locations:

### For MirthConnect
Follow the detailed rollback instructions in `MirthConnect_SwitchOver\README.md`:

1. Stop the MirthConnect service
2. Remove `-Duser.dir` parameter from VM options files
3. Start the service

### For Other Applications
1. **Identify Configuration Method**: Determine how the application was configured to use Config folder
2. **Revert Configuration**: Remove or modify the configuration that points to Config folder
3. **Restart Application**: Restart the application to use original configuration
4. **Verify Rollback**: Confirm application is using original configuration files

## Troubleshooting

### Script Execution Issues

**Error: Execution Policy Restriction**
```powershell
# Solution: Set execution policy (run as Administrator)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**Error: Path Not Found**
```powershell
# Solution: Verify you're in the correct directory
Get-Location
# Should show: C:\Mobile Aspects\Script\Util_CopyConfigFiles
```

### File Copy Issues

**Error: Access Denied**
- **Cause**: Insufficient permissions to read source or write to destination
- **Solution**: Run PowerShell as Administrator or check file permissions

**Error: File Already Exists**
- **Cause**: Destination files already exist
- **Solution**: Use interactive options (Y/N/A/S) or delete destination files first

### Missing Files After Copy

**Issue**: Expected files not copied
- **Check Patterns**: Verify file extensions match the defined patterns
- **Check Exclusions**: Ensure files aren't in excluded directories
- **Check Permissions**: Verify read access to source files

## Script Parameters

### Default Parameters
```powershell
param(
    [string]$SourceRoot = "../..",      # Mobile Aspects root directory
    [string]$ConfigRoot = "../../Config" # Config destination directory
)
```

### Parameter Descriptions

**$SourceRoot**
- **Purpose**: Root directory to search for configuration files
- **Default**: `"../.."`  (Mobile Aspects root from script location)
- **Example**: `"..\..\MyApplication"`

**$ConfigRoot**
- **Purpose**: Destination directory for copied configuration files
- **Default**: `"../../Config"` (Config folder in Mobile Aspects root)
- **Example**: `"..\..\Backup\Config"`

### Using Custom Parameters
```powershell
# Copy from specific application to custom destination
.\Util_CopyConfigFiles.ps1 -SourceRoot "..\..\MirthConnect\CURR" -ConfigRoot "..\..\Backup\MirthConnect"

# Copy everything to a different Config location
.\Util_CopyConfigFiles.ps1 -ConfigRoot "..\..\Config_Backup"
```

## Best Practices

### Before Running the Script
1. **Backup Original Files**: Create backups of important configuration files
2. **Stop Applications**: Stop applications that use the configuration files
3. **Check Disk Space**: Ensure sufficient space in destination directory
4. **Test in Non-Production**: Test the process in a development environment first

### After Running the Script
1. **Verify File Integrity**: Compare checksums of critical configuration files
2. **Test Applications**: Verify applications work with copied configuration
3. **Document Changes**: Record what was copied and when
4. **Monitor Applications**: Watch for any configuration-related issues

### Maintenance
1. **Regular Updates**: Re-run the script when configuration files change
2. **Clean Up**: Remove obsolete configuration files from Config folder
3. **Version Control**: Consider using Git to track Config folder changes
4. **Backup Strategy**: Include Config folder in regular backup procedures

## Related Documentation

- **MirthConnect Switchover**: See `MirthConnect_SwitchOver\README.md` for detailed MirthConnect-specific instructions
- **PowerShell Execution Policies**: [Microsoft Documentation](https://docs.microsoft.com/en-us/powershell/module/microsoft.powershell.core/about/about_execution_policies)
- **File System Security**: [Windows File Permissions](https://docs.microsoft.com/en-us/windows/security/threat-protection/security-policy-settings/file-system)

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the script output for specific error messages
3. Verify file permissions and execution policies
4. Test with a smaller subset of files to isolate issues