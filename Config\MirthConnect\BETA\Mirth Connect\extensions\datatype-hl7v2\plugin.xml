<pluginMetaData path="datatype-hl7v2">
	<name>HL7v2 Data Type</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This plugin provides support for the HL7v2 data type</description>
	<serverClasses>
		<string>com.mirth.connect.plugins.datatypes.hl7v2.HL7v2DataTypeServerPlugin</string>
	</serverClasses>
	<clientClasses>
		<string>com.mirth.connect.plugins.datatypes.hl7v2.HL7v2DataTypeClientPlugin</string>
	</clientClasses>
	<templateClassName>com.mirth.connect.plugins.datatypes.hl7v2.HL7v2DataTypeCodeTemplatePlugin</templateClassName>
	<library type="CLIENT" path="datatype-hl7v2-client.jar" />
	<library type="SHARED" path="datatype-hl7v2-shared.jar" />
	<library type="SERVER" path="datatype-hl7v2-server.jar" />
</pluginMetaData>
