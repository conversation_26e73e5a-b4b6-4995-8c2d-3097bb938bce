<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:21 PDT 2023 -->
<title>RawMessage</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RawMessage";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":42,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":42,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/NCPDPUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/ResponseFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/RawMessage.html" target="_top">Frames</a></li>
<li><a href="RawMessage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.server.userutil</div>
<h2 title="Class RawMessage" class="title">Class RawMessage</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.server.userutil.RawMessage</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">RawMessage</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">This class represents a raw message as it is received by a channel, and is used to retrieve
 details such as the raw data or source map.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#RawMessage-byte:A-">RawMessage</a></span>(byte[]&nbsp;rawBytes)</code>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#RawMessage-byte:A-java.util.Collection-">RawMessage</a></span>(byte[]&nbsp;rawBytes,
          <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&gt;&nbsp;destinationMetaDataIds)</code>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#RawMessage-byte:A-java.util.Collection-java.util.Map-">RawMessage</a></span>(byte[]&nbsp;rawBytes,
          <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&gt;&nbsp;destinationMetaDataIds,
          <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;sourceMap)</code>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#RawMessage-java.lang.String-">RawMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;rawData)</code>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#RawMessage-java.lang.String-java.util.Collection-">RawMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;rawData,
          <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&gt;&nbsp;destinationMetaDataIds)</code>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#RawMessage-java.lang.String-java.util.Collection-java.util.Map-">RawMessage</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;rawData,
          <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&gt;&nbsp;destinationMetaDataIds,
          <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;sourceMap)</code>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#clearMessage--">clearMessage</a></span>()</code>
<div class="block">Removes references to any data (textual or binary) currently stored by the raw message.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#getChannelMap--">getChannelMap</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#getSourceMap--"><code>getSourceMap()</code></a> instead.</span></div>
</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#getDestinationMetaDataIds--">getDestinationMetaDataIds</a></span>()</code>
<div class="block">Returns the collection of integers (metadata IDs) representing which destinations to dispatch
 the message to.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#getRawBytes--">getRawBytes</a></span>()</code>
<div class="block">Returns the binary data (byte array) to be dispatched to a channel.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#getRawData--">getRawData</a></span>()</code>
<div class="block">Returns the textual data to be dispatched to a channel.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#getSourceMap--">getSourceMap</a></span>()</code>
<div class="block">Returns the source map to be used at the beginning of the channel dispatch.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#isBinary--">isBinary</a></span>()</code>
<div class="block">Returns a Boolean representing whether this object contains textual or binary data.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#setChannelMap-java.util.Map-">setChannelMap</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;channelMap)</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#setSourceMap-java.util.Map-"><code>setSourceMap(sourceMap)</code></a> instead.</span></div>
</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#setDestinationMetaDataIds-java.util.Collection-">setDestinationMetaDataIds</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&gt;&nbsp;destinationMetaDataIds)</code>
<div class="block">Sets which destinations to dispatch the message to.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#setSourceMap-java.util.Map-">setSourceMap</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;sourceMap)</code>
<div class="block">Sets the source map to be used at the beginning of the channel dispatch.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RawMessage-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RawMessage</h4>
<pre>public&nbsp;RawMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;rawData)</pre>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rawData</code> - The textual data to dispatch to the channel.</dd>
</dl>
</li>
</ul>
<a name="RawMessage-java.lang.String-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RawMessage</h4>
<pre>public&nbsp;RawMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;rawData,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&gt;&nbsp;destinationMetaDataIds)</pre>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rawData</code> - The textual data to dispatch to the channel.</dd>
<dd><code>destinationMetaDataIds</code> - A collection of integers (metadata IDs) representing which destinations to
            dispatch the message to. JavaScript arrays can be used.</dd>
</dl>
</li>
</ul>
<a name="RawMessage-java.lang.String-java.util.Collection-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RawMessage</h4>
<pre>public&nbsp;RawMessage(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;rawData,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&gt;&nbsp;destinationMetaDataIds,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;sourceMap)</pre>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rawData</code> - The textual data to dispatch to the channel.</dd>
<dd><code>destinationMetaDataIds</code> - A collection of integers (metadata IDs) representing which destinations to
            dispatch the message to. JavaScript arrays can be used.</dd>
<dd><code>sourceMap</code> - Any values placed in this map will be populated in the source map at the beginning
            of the message's lifecycle.</dd>
</dl>
</li>
</ul>
<a name="RawMessage-byte:A-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RawMessage</h4>
<pre>public&nbsp;RawMessage(byte[]&nbsp;rawBytes)</pre>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rawBytes</code> - The binary data (byte array) to dispatch to the channel.</dd>
</dl>
</li>
</ul>
<a name="RawMessage-byte:A-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>RawMessage</h4>
<pre>public&nbsp;RawMessage(byte[]&nbsp;rawBytes,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&gt;&nbsp;destinationMetaDataIds)</pre>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rawBytes</code> - The binary data (byte array) to dispatch to the channel.</dd>
<dd><code>destinationMetaDataIds</code> - A collection of integers (metadata IDs) representing which destinations to
            dispatch the message to. JavaScript arrays can be used.</dd>
</dl>
</li>
</ul>
<a name="RawMessage-byte:A-java.util.Collection-java.util.Map-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RawMessage</h4>
<pre>public&nbsp;RawMessage(byte[]&nbsp;rawBytes,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&gt;&nbsp;destinationMetaDataIds,
                  <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;sourceMap)</pre>
<div class="block">Instantiates a RawMessage object to dispatch to a channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>rawBytes</code> - The binary data (byte array) to dispatch to the channel.</dd>
<dd><code>destinationMetaDataIds</code> - A collection of integers (metadata IDs) representing which destinations to
            dispatch the message to. JavaScript arrays can be used.</dd>
<dd><code>sourceMap</code> - Any values placed in this map will be populated in the source map at the beginning
            of the message's lifecycle.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getRawData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRawData</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRawData()</pre>
<div class="block">Returns the textual data to be dispatched to a channel.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The textual data to be dispatched to a channel.</dd>
</dl>
</li>
</ul>
<a name="getRawBytes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRawBytes</h4>
<pre>public&nbsp;byte[]&nbsp;getRawBytes()</pre>
<div class="block">Returns the binary data (byte array) to be dispatched to a channel.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The binary data (byte array) to be dispatched to a channel.</dd>
</dl>
</li>
</ul>
<a name="getDestinationMetaDataIds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinationMetaDataIds</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;getDestinationMetaDataIds()</pre>
<div class="block">Returns the collection of integers (metadata IDs) representing which destinations to dispatch
 the message to.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The collection of integers (metadata IDs) representing which destinations to dispatch
         the message to.</dd>
</dl>
</li>
</ul>
<a name="setDestinationMetaDataIds-java.util.Collection-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDestinationMetaDataIds</h4>
<pre>public&nbsp;void&nbsp;setDestinationMetaDataIds(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&gt;&nbsp;destinationMetaDataIds)</pre>
<div class="block">Sets which destinations to dispatch the message to.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>destinationMetaDataIds</code> - A list of integers (metadata IDs) representing which destinations to dispatch the
            message to.</dd>
</dl>
</li>
</ul>
<a name="getChannelMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChannelMap</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getChannelMap()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#getSourceMap--"><code>getSourceMap()</code></a> instead.</span></div>
<div class="block">Returns the channel map to be used at the beginning of the channel dispatch.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The channel map to be used at the beginning of the channel dispatch.</dd>
</dl>
</li>
</ul>
<a name="setChannelMap-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setChannelMap</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public&nbsp;void&nbsp;setChannelMap(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;channelMap)</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html#setSourceMap-java.util.Map-"><code>setSourceMap(sourceMap)</code></a> instead.</span></div>
<div class="block">Sets the channel map to be used at the beginning of the channel dispatch.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelMap</code> - Any values placed in this map will be populated in the channel map at the
            beginning of the message's lifecycle.</dd>
</dl>
</li>
</ul>
<a name="getSourceMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSourceMap</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;getSourceMap()</pre>
<div class="block">Returns the source map to be used at the beginning of the channel dispatch.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The source map to be used at the beginning of the channel dispatch.</dd>
</dl>
</li>
</ul>
<a name="setSourceMap-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSourceMap</h4>
<pre>public&nbsp;void&nbsp;setSourceMap(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&gt;&nbsp;sourceMap)</pre>
<div class="block">Sets the source map to be used at the beginning of the channel dispatch.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>sourceMap</code> - Any values placed in this map will be populated in the source map at the beginning
            of the message's lifecycle.</dd>
</dl>
</li>
</ul>
<a name="isBinary--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBinary</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isBinary()</pre>
<div class="block">Returns a Boolean representing whether this object contains textual or binary data.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A Boolean representing whether this object contains textual or binary data.</dd>
</dl>
</li>
</ul>
<a name="clearMessage--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>clearMessage</h4>
<pre>public&nbsp;void&nbsp;clearMessage()</pre>
<div class="block">Removes references to any data (textual or binary) currently stored by the raw message.</div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/NCPDPUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/ResponseFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/RawMessage.html" target="_top">Frames</a></li>
<li><a href="RawMessage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
