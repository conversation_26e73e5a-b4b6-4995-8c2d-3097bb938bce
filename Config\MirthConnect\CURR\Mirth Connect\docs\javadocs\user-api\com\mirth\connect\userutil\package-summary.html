<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:21 PDT 2023 -->
<title>com.mirth.connect.userutil</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.mirth.connect.userutil";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/mirth/connect/server/userutil/package-summary.html">Prev&nbsp;Package</a></li>
<li>Next&nbsp;Package</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/mirth/connect/userutil/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;com.mirth.connect.userutil</h1>
<div class="docSummary">
<div class="block">This package is included in the JavaScript scope on both the client and the server.&nbsp;Classes
 in this package are part of the supported User API for use in channels/scripts.&nbsp;Reference to
 any class in Mirth Connect outside of the userutil packages is unsupported.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/AttachmentEntry.html" title="class in com.mirth.connect.userutil">AttachmentEntry</a></td>
<td class="colLast">
<div class="block">Used to store and retrieve details about message attachments such as the name, contents, and
 MIME type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/ImmutableAttachment.html" title="class in com.mirth.connect.userutil">ImmutableAttachment</a></td>
<td class="colLast">
<div class="block">This class represents an message attachment and is used to retrieve details such as the
 replacement token or content type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></td>
<td class="colLast">
<div class="block">This class represents a connector message and is used to retrieve details such as the message ID,
 metadata ID, status, and various content types.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html" title="class in com.mirth.connect.userutil">ImmutableMessage</a></td>
<td class="colLast">
<div class="block">This class represents an overall message and is used to retrieve details such as the message ID,
 specific connector messages, or the merged connector message.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil">ImmutableMessageContent</a></td>
<td class="colLast">
<div class="block">This class represents content associated with a connector message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/JsonUtil.html" title="class in com.mirth.connect.userutil">JsonUtil</a></td>
<td class="colLast">
<div class="block">Provides JSON utility methods.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/ListBuilder.html" title="class in com.mirth.connect.userutil">ListBuilder</a></td>
<td class="colLast">
<div class="block">Convenience class to allow fluent building of lists.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/Lists.html" title="class in com.mirth.connect.userutil">Lists</a></td>
<td class="colLast">
<div class="block">Convenience class to allow fluent building of lists.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/MapBuilder.html" title="class in com.mirth.connect.userutil">MapBuilder</a></td>
<td class="colLast">
<div class="block">Convenience class to allow fluent building of maps.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/Maps.html" title="class in com.mirth.connect.userutil">Maps</a></td>
<td class="colLast">
<div class="block">Convenience class to allow fluent building of maps.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/MessageHeaders.html" title="class in com.mirth.connect.userutil">MessageHeaders</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/MessageParameters.html" title="class in com.mirth.connect.userutil">MessageParameters</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/Response.html" title="class in com.mirth.connect.userutil">Response</a></td>
<td class="colLast">
<div class="block">This class represents a channel or destination response and is used to retrieve details such as
 the response data, message status, and errors.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/ResponseMap.html" title="class in com.mirth.connect.userutil">ResponseMap</a></td>
<td class="colLast">
<div class="block">A wrapper class for the response map which allows users to retrieve values using the proper "d#"
 key (where "#" is the destination connector's metadata ID), or by using the actual destination
 name.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/XmlUtil.html" title="class in com.mirth.connect.userutil">XmlUtil</a></td>
<td class="colLast">
<div class="block">Provides XML utility methods.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/ContentType.html" title="enum in com.mirth.connect.userutil">ContentType</a></td>
<td class="colLast">
<div class="block">Denotes various types of content created by a channel.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a></td>
<td class="colLast">
<div class="block">Denotes the status of a connector message or response.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package com.mirth.connect.userutil Description">Package com.mirth.connect.userutil Description</h2>
<div class="block">This package is included in the JavaScript scope on both the client and the server.&nbsp;Classes
 in this package are part of the supported User API for use in channels/scripts.&nbsp;Reference to
 any class in Mirth Connect outside of the userutil packages is unsupported.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/mirth/connect/server/userutil/package-summary.html">Prev&nbsp;Package</a></li>
<li>Next&nbsp;Package</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/mirth/connect/userutil/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
