<pluginMetaData path="xsltstep">
	<name>XSLT Transformer Step</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This plugin provides a XSLT support step-type for Mirth Connect transformers</description>
	<clientClasses>
		<string>com.mirth.connect.plugins.xsltstep.XsltStepPlugin</string>
	</clientClasses>
	<library type="CLIENT" path="xsltstep-client.jar" />
	<library type="SHARED" path="xsltstep-shared.jar" />
</pluginMetaData>
