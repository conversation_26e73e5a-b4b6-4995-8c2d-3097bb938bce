<connectorMetaData path="http">
	<name>HTTP Listener</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to listen for incoming HTTP data. Messages are received as XML and include the full header contents.</description>
	<clientClassName>com.mirth.connect.connectors.http.HttpListener</clientClassName>
	<serverClassName>com.mirth.connect.connectors.http.HttpReceiver</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.http.HttpReceiverProperties</sharedClassName>
	<templateClassName>com.mirth.connect.connectors.http.HttpListenerCodeTemplatePlugin</templateClassName>
	<library type="CLIENT" path="http-client.jar" />
	<library type="SHARED" path="http-shared.jar" />
	<library type="SERVER" path="http-server.jar" />
	<transformers></transformers>
	<protocol>http</protocol>
	<type>SOURCE</type>
</connectorMetaData>
