<connectorMetaData path="vm">
	<name>Channel Reader</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to listen for incoming events from other channels in the same Mirth Connect instance.</description>
	<clientClassName>com.mirth.connect.connectors.vm.ChannelReader</clientClassName>
	<serverClassName>com.mirth.connect.connectors.vm.VmReceiver</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.vm.VmReceiverProperties</sharedClassName>
	<library type="CLIENT" path="vm-client.jar" />
	<library type="SHARED" path="vm-shared.jar" />
	<library type="SERVER" path="vm-server.jar" />
	<transformers></transformers>
	<protocol>vm</protocol>
	<type>SOURCE</type>
</connectorMetaData>
