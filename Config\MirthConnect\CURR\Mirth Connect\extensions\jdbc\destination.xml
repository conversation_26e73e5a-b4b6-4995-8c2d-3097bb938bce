<connectorMetaData path="jdbc">
	<name>Database Writer</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to write to any JDBC-compatabile database with Insert, Update or JavaScript statements.</description>
	<clientClassName>com.mirth.connect.connectors.jdbc.DatabaseWriter</clientClassName>
	<serverClassName>com.mirth.connect.connectors.jdbc.DatabaseDispatcher</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.jdbc.DatabaseDispatcherProperties</sharedClassName>
	<library type="CLIENT" path="jdbc-client.jar" />
	<library type="SHARED" path="jdbc-shared.jar" />
	<library type="SERVER" path="jdbc-server.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.connectors.jdbc.DatabaseConnectorServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.connectors.jdbc.DatabaseConnectorServlet"/>
	<protocol>jdbc</protocol>
	<type>DESTINATION</type>
</connectorMetaData>
