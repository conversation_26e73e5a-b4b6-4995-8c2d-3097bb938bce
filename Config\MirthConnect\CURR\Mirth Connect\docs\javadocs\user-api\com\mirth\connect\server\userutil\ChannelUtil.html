<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:18 PDT 2023 -->
<title>ChannelUtil</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ChannelUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9,"i15":9,"i16":9,"i17":9,"i18":9,"i19":9,"i20":9,"i21":9,"i22":9,"i23":9,"i24":9,"i25":9,"i26":9,"i27":9,"i28":9,"i29":9,"i30":9,"i31":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/ContextFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/ChannelUtil.html" target="_top">Frames</a></li>
<li><a href="ChannelUtil.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.server.userutil</div>
<h2 title="Class ChannelUtil" class="title">Class ChannelUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.server.userutil.ChannelUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ChannelUtil</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">This utility class allows the user to query information from channels or to perform actions on
 channels.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#deployChannel-java.lang.String-">deployChannel</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Deploy a channel.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getChannelIds--">getChannelIds</a></span>()</code>
<div class="block">Get all channel Ids.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getChannelName-java.lang.String-">getChannelName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId)</code>
<div class="block">Get the name for a channel.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getChannelNames--">getChannelNames</a></span>()</code>
<div class="block">Get all channels names.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/DeployedState.html" title="enum in com.mirth.connect.server.userutil">DeployedState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getChannelState-java.lang.String-">getChannelState</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Get the current state of a channel.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/DeployedState.html" title="enum in com.mirth.connect.server.userutil">DeployedState</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getConnectorState-java.lang.String-java.lang.Number-">getConnectorState</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;metaDataId)</code>
<div class="block">Get the current state of a connector.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getDeployedChannelId-java.lang.String-">getDeployedChannelId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelName)</code>
<div class="block">Get the id for a deployed channel.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getDeployedChannelIds--">getDeployedChannelIds</a></span>()</code>
<div class="block">Get all deployed channel Ids.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getDeployedChannelName-java.lang.String-">getDeployedChannelName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId)</code>
<div class="block">Get the name for a deployed channel.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getDeployedChannelNames--">getDeployedChannelNames</a></span>()</code>
<div class="block">Get all deployed channels names.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getErrorCount-java.lang.String-">getErrorCount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Get the error count statistic for a specific channel.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getErrorCount-java.lang.String-java.lang.Number-">getErrorCount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;metaDataId)</code>
<div class="block">Get the error count statistic for a specific connector.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getFilteredCount-java.lang.String-">getFilteredCount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Get the filtered count statistic for a specific channel.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getFilteredCount-java.lang.String-java.lang.Number-">getFilteredCount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;metaDataId)</code>
<div class="block">Get the filtered count statistic for a specific connector.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getQueuedCount-java.lang.String-">getQueuedCount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Get the queued count statistic for a specific channel.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getQueuedCount-java.lang.String-java.lang.Number-">getQueuedCount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;metaDataId)</code>
<div class="block">Get the queued count statistic for a specific connector.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getReceivedCount-java.lang.String-">getReceivedCount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Get the received count statistic for a specific channel.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getReceivedCount-java.lang.String-java.lang.Number-">getReceivedCount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;metaDataId)</code>
<div class="block">Get the received count statistic for a specific connector.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getSentCount-java.lang.String-">getSentCount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Get the sent count statistic for a specific channel.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#getSentCount-java.lang.String-java.lang.Number-">getSentCount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
            <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;metaDataId)</code>
<div class="block">Get the sent count statistic for a specific connector.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#haltChannel-java.lang.String-">haltChannel</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Halt a deployed channel.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>static boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#isChannelDeployed-java.lang.String-">isChannelDeployed</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Check if a channel is currently deployed.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#pauseChannel-java.lang.String-">pauseChannel</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Pause a deployed channel.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#resetStatistics-java.lang.String-">resetStatistics</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Reset all statistics for a specific channel.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#resetStatistics-java.lang.String-java.lang.Integer-">resetStatistics</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;metaDataId)</code>
<div class="block">Reset all statistics for the specified connector on the given channel.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#resetStatistics-java.lang.String-java.lang.Integer-java.util.Collection-">resetStatistics</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;metaDataId,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="../../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a>&gt;&nbsp;statuses)</code>
<div class="block">Reset the specified statistics for the specified connector on the given channel.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#resumeChannel-java.lang.String-">resumeChannel</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Resume a deployed channel.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#startChannel-java.lang.String-">startChannel</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Start a deployed channel.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#startConnector-java.lang.String-java.lang.Integer-">startConnector</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;metaDataId)</code>
<div class="block">Start a connector on a given channel.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#stopChannel-java.lang.String-">stopChannel</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Stop a deployed channel.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#stopConnector-java.lang.String-java.lang.Integer-">stopConnector</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;metaDataId)</code>
<div class="block">Stop a connector on a given channel.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code>static <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html#undeployChannel-java.lang.String-">undeployChannel</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</code>
<div class="block">Undeploy a channel.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getChannelNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChannelNames</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getChannelNames()</pre>
<div class="block">Get all channels names.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A list of all channel names.</dd>
</dl>
</li>
</ul>
<a name="getChannelIds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChannelIds</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getChannelIds()</pre>
<div class="block">Get all channel Ids.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A list of all channel Ids.</dd>
</dl>
</li>
</ul>
<a name="getDeployedChannelNames--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeployedChannelNames</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getDeployedChannelNames()</pre>
<div class="block">Get all deployed channels names.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A list of all deployed channel names.</dd>
</dl>
</li>
</ul>
<a name="getChannelName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChannelName</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getChannelName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId)</pre>
<div class="block">Get the name for a channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The channel id of the channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The channel name of the specified channel.</dd>
</dl>
</li>
</ul>
<a name="getDeployedChannelIds--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeployedChannelIds</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getDeployedChannelIds()</pre>
<div class="block">Get all deployed channel Ids.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A list of all deployed channel Ids.</dd>
</dl>
</li>
</ul>
<a name="getDeployedChannelName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeployedChannelName</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDeployedChannelName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelId)</pre>
<div class="block">Get the name for a deployed channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelId</code> - The channel id of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The channel name of the specified channel.</dd>
</dl>
</li>
</ul>
<a name="getDeployedChannelId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDeployedChannelId</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDeployedChannelId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelName)</pre>
<div class="block">Get the id for a deployed channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelName</code> - The channel name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The channel Id of the specified channel.</dd>
</dl>
</li>
</ul>
<a name="startChannel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startChannel</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;&nbsp;startChannel(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)
                                 throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Start a deployed channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><code>Future</code></a> object representing the result of the asynchronous operation. You
         can call <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--"><code>get()</code></a> or <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-"><code>get(timeoutInMillis)</code></a>
         to wait for the operation to finish.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If the task cannot be scheduled for execution.</dd>
</dl>
</li>
</ul>
<a name="stopChannel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopChannel</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;&nbsp;stopChannel(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)
                                throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Stop a deployed channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><code>Future</code></a> object representing the result of the asynchronous operation. You
         can call <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--"><code>get()</code></a> or <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-"><code>get(timeoutInMillis)</code></a>
         to wait for the operation to finish.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If the task cannot be scheduled for execution.</dd>
</dl>
</li>
</ul>
<a name="pauseChannel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pauseChannel</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;&nbsp;pauseChannel(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)
                                 throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Pause a deployed channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><code>Future</code></a> object representing the result of the asynchronous operation. You
         can call <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--"><code>get()</code></a> or <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-"><code>get(timeoutInMillis)</code></a>
         to wait for the operation to finish.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If the task cannot be scheduled for execution.</dd>
</dl>
</li>
</ul>
<a name="resumeChannel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resumeChannel</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;&nbsp;resumeChannel(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)
                                  throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Resume a deployed channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><code>Future</code></a> object representing the result of the asynchronous operation. You
         can call <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--"><code>get()</code></a> or <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-"><code>get(timeoutInMillis)</code></a>
         to wait for the operation to finish.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If the task cannot be scheduled for execution.</dd>
</dl>
</li>
</ul>
<a name="haltChannel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>haltChannel</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;&nbsp;haltChannel(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)
                                throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Halt a deployed channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><code>Future</code></a> object representing the result of the asynchronous operation. You
         can call <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--"><code>get()</code></a> or <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-"><code>get(timeoutInMillis)</code></a>
         to wait for the operation to finish.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If the task cannot be scheduled for execution.</dd>
</dl>
</li>
</ul>
<a name="getChannelState-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChannelState</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/DeployedState.html" title="enum in com.mirth.connect.server.userutil">DeployedState</a>&nbsp;getChannelState(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</pre>
<div class="block">Get the current state of a channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The current DeployedState.</dd>
</dl>
</li>
</ul>
<a name="deployChannel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>deployChannel</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;&nbsp;deployChannel(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</pre>
<div class="block">Deploy a channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><code>Future</code></a> object representing the result of the asynchronous operation. You
         can call <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--"><code>get()</code></a> or <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-"><code>get(timeoutInMillis)</code></a>
         to wait for the operation to finish.</dd>
</dl>
</li>
</ul>
<a name="undeployChannel-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>undeployChannel</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;&nbsp;undeployChannel(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</pre>
<div class="block">Undeploy a channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><code>Future</code></a> object representing the result of the asynchronous operation. You
         can call <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--"><code>get()</code></a> or <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-"><code>get(timeoutInMillis)</code></a>
         to wait for the operation to finish.</dd>
</dl>
</li>
</ul>
<a name="isChannelDeployed-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isChannelDeployed</h4>
<pre>public static&nbsp;boolean&nbsp;isChannelDeployed(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</pre>
<div class="block">Check if a channel is currently deployed.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>True if the channel is deployed, false if it is not.</dd>
</dl>
</li>
</ul>
<a name="startConnector-java.lang.String-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startConnector</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;&nbsp;startConnector(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;metaDataId)
                                   throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Start a connector on a given channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the channel.</dd>
<dd><code>metaDataId</code> - The metadata id of the connector. Note that the source connector has a metadata id
            of 0.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><code>Future</code></a> object representing the result of the asynchronous operation. You
         can call <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--"><code>get()</code></a> or <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-"><code>get(timeoutInMillis)</code></a>
         to wait for the operation to finish.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If the task cannot be scheduled for execution.</dd>
</dl>
</li>
</ul>
<a name="stopConnector-java.lang.String-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>stopConnector</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;&nbsp;stopConnector(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                                         <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;metaDataId)
                                  throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Stop a connector on a given channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the channel.</dd>
<dd><code>metaDataId</code> - The metadata id of the connector. Note that the source connector has a metadata id
            of 0.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><code>Future</code></a> object representing the result of the asynchronous operation. You
         can call <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--"><code>get()</code></a> or <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-"><code>get(timeoutInMillis)</code></a>
         to wait for the operation to finish.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If the task cannot be scheduled for execution.</dd>
</dl>
</li>
</ul>
<a name="getConnectorState-java.lang.String-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConnectorState</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/DeployedState.html" title="enum in com.mirth.connect.server.userutil">DeployedState</a>&nbsp;getConnectorState(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                                              <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;metaDataId)</pre>
<div class="block">Get the current state of a connector.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the channel.</dd>
<dd><code>metaDataId</code> - The metadata id of the connector. Note that the source connector has a metadata id
            of 0.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The current connector state returned as the DeployedState enumerator.</dd>
</dl>
</li>
</ul>
<a name="getReceivedCount-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReceivedCount</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getReceivedCount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</pre>
<div class="block">Get the received count statistic for a specific channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The received count statistic as a Long for the specified channel.</dd>
</dl>
</li>
</ul>
<a name="getReceivedCount-java.lang.String-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReceivedCount</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getReceivedCount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;metaDataId)</pre>
<div class="block">Get the received count statistic for a specific connector.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dd><code>metaDataId</code> - The metadata id of the connector. Note that the source connector has a metadata id
            of 0.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The received count statistic as a Long for the specified connector.</dd>
</dl>
</li>
</ul>
<a name="getFilteredCount-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFilteredCount</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getFilteredCount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</pre>
<div class="block">Get the filtered count statistic for a specific channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The filtered count statistic as a Long for the specified channel.</dd>
</dl>
</li>
</ul>
<a name="getFilteredCount-java.lang.String-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFilteredCount</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getFilteredCount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                                    <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;metaDataId)</pre>
<div class="block">Get the filtered count statistic for a specific connector.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dd><code>metaDataId</code> - The metadata id of the connector. Note that the source connector has a metadata id
            of 0.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The filtered count statistic as a Long for the specified connector.</dd>
</dl>
</li>
</ul>
<a name="getQueuedCount-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQueuedCount</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getQueuedCount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</pre>
<div class="block">Get the queued count statistic for a specific channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The queued count statistic as a Long for the specified channel.</dd>
</dl>
</li>
</ul>
<a name="getQueuedCount-java.lang.String-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getQueuedCount</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getQueuedCount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                                  <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;metaDataId)</pre>
<div class="block">Get the queued count statistic for a specific connector.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dd><code>metaDataId</code> - The metadata id of the connector. Note that the source connector has a metadata id
            of 0.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The queued count statistic as a Long for the specified connector.</dd>
</dl>
</li>
</ul>
<a name="getSentCount-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSentCount</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getSentCount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</pre>
<div class="block">Get the sent count statistic for a specific channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The sent count statistic as a Long for the specified channel.</dd>
</dl>
</li>
</ul>
<a name="getSentCount-java.lang.String-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSentCount</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getSentCount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                                <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;metaDataId)</pre>
<div class="block">Get the sent count statistic for a specific connector.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dd><code>metaDataId</code> - The metadata id of the connector. Note that the source connector has a metadata id
            of 0.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The sent count statistic as a Long for the specified connector.</dd>
</dl>
</li>
</ul>
<a name="getErrorCount-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getErrorCount</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getErrorCount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)</pre>
<div class="block">Get the error count statistic for a specific channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The error count statistic as a Long for the specified channel.</dd>
</dl>
</li>
</ul>
<a name="getErrorCount-java.lang.String-java.lang.Number-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getErrorCount</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getErrorCount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                                 <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Number.html?is-external=true" title="class or interface in java.lang">Number</a>&nbsp;metaDataId)</pre>
<div class="block">Get the error count statistic for a specific connector.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dd><code>metaDataId</code> - The metadata id of the connector. Note that the source connector has a metadata id
            of 0.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The error count statistic as a Long for the specified connector.</dd>
</dl>
</li>
</ul>
<a name="resetStatistics-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resetStatistics</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;&nbsp;resetStatistics(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName)
                                    throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Reset all statistics for a specific channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><code>Future</code></a> object representing the result of the asynchronous operation. You
         can call <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--"><code>get()</code></a> or <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-"><code>get(timeoutInMillis)</code></a>
         to wait for the operation to finish.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If the task cannot be scheduled for execution.</dd>
</dl>
</li>
</ul>
<a name="resetStatistics-java.lang.String-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resetStatistics</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;&nbsp;resetStatistics(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                                           <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;metaDataId)
                                    throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Reset all statistics for the specified connector on the given channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dd><code>metaDataId</code> - The metadata id of the deployed connector. Note that the source connector has a
            metadata id of 0 and the aggregate of null.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><code>Future</code></a> object representing the result of the asynchronous operation. You
         can call <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--"><code>get()</code></a> or <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-"><code>get(timeoutInMillis)</code></a>
         to wait for the operation to finish.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If the task cannot be scheduled for execution.</dd>
</dl>
</li>
</ul>
<a name="resetStatistics-java.lang.String-java.lang.Integer-java.util.Collection-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>resetStatistics</h4>
<pre>public static&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Void.html?is-external=true" title="class or interface in java.lang">Void</a>&gt;&nbsp;resetStatistics(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;channelIdOrName,
                                           <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;metaDataId,
                                           <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Collection.html?is-external=true" title="class or interface in java.util">Collection</a>&lt;<a href="../../../../../com/mirth/connect/userutil/Status.html" title="enum in com.mirth.connect.userutil">Status</a>&gt;&nbsp;statuses)
                                    throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></pre>
<div class="block">Reset the specified statistics for the specified connector on the given channel.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>channelIdOrName</code> - The channel id or current name of the deployed channel.</dd>
<dd><code>metaDataId</code> - The metadata id of the deployed connector. Note that the source connector has a
            metadata id of 0 and the aggregate of null.</dd>
<dd><code>statuses</code> - A collection of statuses to reset.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A <a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil"><code>Future</code></a> object representing the result of the asynchronous operation. You
         can call <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--"><code>get()</code></a> or <a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-"><code>get(timeoutInMillis)</code></a>
         to wait for the operation to finish.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Exception.html?is-external=true" title="class or interface in java.lang">Exception</a></code> - If the task cannot be scheduled for execution.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/ContextFactory.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/ChannelUtil.html" target="_top">Frames</a></li>
<li><a href="ChannelUtil.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
