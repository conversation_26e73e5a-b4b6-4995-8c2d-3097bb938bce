<!-- 
	Database driver information
	class = the driver class name, cannot be empty
	name = database driver name to be displayed as, cannot be empty
	template = the template for creating the database connection, cannot be empty
	selectLimit = defines the select statement used for retrieving column information, empty means use the generic query (which could be slow)
	alternativeClasses = A comma-separated list of legacy driver classes (optional).
-->
<drivers>
	<driver class="com.mysql.cj.jdbc.Driver" name="MySQL" template="*****************************" selectLimit="SELECT * FROM ? LIMIT 1" alternativeClasses="com.mysql.jdbc.Driver" />
	<driver class="oracle.jdbc.driver.OracleDriver" name="Oracle" template="**********************************" selectLimit="SELECT * FROM ? WHERE ROWNUM &lt; 2" />
	<driver class="org.postgresql.Driver" name="PostgreSQL" template="**********************************" selectLimit="SELECT * FROM ? LIMIT 1" />
	<driver class="net.sourceforge.jtds.jdbc.Driver" name="SQL Server/Sybase (jTDS)" template="**************************************" selectLimit="SELECT TOP 1 * FROM ?" />
	<driver class="com.microsoft.sqlserver.jdbc.SQLServerDriver" name="Microsoft SQL Server" template="**********************************************" selectLimit="SELECT TOP 1 * FROM ?" />
	<driver class="org.sqlite.JDBC" name="SQLite" template="jdbc:sqlite:dbfile.db" selectLimit="SELECT * FROM ? LIMIT 1" />
</drivers>