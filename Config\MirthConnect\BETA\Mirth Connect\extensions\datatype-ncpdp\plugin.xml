<pluginMetaData path="datatype-ncpdp">
	<name>NCPDP Data Type</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This plugin provides support for the NCPDP data type</description>
	<serverClasses>
		<string>com.mirth.connect.plugins.datatypes.ncpdp.NCPDPDataTypeServerPlugin</string>
	</serverClasses>
	<clientClasses>
		<string>com.mirth.connect.plugins.datatypes.ncpdp.NCPDPDataTypeClientPlugin</string>
	</clientClasses>
	<templateClassName>com.mirth.connect.plugins.datatypes.ncpdp.NCPDPDataTypeCodeTemplatePlugin</templateClassName>
	<library type="CLIENT" path="datatype-ncpdp-client.jar" />
	<library type="SHARED" path="datatype-ncpdp-shared.jar" />
	<library type="SERVER" path="datatype-ncpdp-server.jar" />
</pluginMetaData>
