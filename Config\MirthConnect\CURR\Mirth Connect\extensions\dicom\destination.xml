<connectorMetaData path="dicom">
	<name>DICOM Sender</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.4.2</pluginVersion>
	<mirthVersion>4.4.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This connector allows Mirth Connect to send messages using DICOM protocol.</description>
	<clientClassName>com.mirth.connect.connectors.dimse.DICOMSender</clientClassName>
	<serverClassName>com.mirth.connect.connectors.dimse.DICOMDispatcher</serverClassName>
	<sharedClassName>com.mirth.connect.connectors.dimse.DICOMDispatcherProperties</sharedClassName>
	<library type="CLIENT" path="dicom-client.jar" />
	<library type="SHARED" path="dicom-shared.jar" />
	<library type="SHARED" path="lib/dcm4che-core-2.0.29.jar" />
	<library type="SERVER" path="dicom-server.jar" />
	<library type="SERVER" path="lib/jai_imageio.jar" />
	<library type="SERVER" path="lib/dcm4che-net-2.0.29.jar" />
	<library type="SERVER" path="lib/dcm4che-tool-dcmrcv-2.0.29.jar" />
	<library type="SERVER" path="lib/dcm4che-tool-dcmsnd-2.0.29.jar" />
	<library type="SERVER" path="lib/dcm4che-filecache-2.0.29.jar" />
	<transformers></transformers>
	<protocol>dicom</protocol>
	<type>DESTINATION</type>
</connectorMetaData>
