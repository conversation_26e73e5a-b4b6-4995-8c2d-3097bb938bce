<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:21 PDT 2023 -->
<title>com.mirth.connect.server.userutil</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="com.mirth.connect.server.userutil";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/plugins/httpauth/userutil/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../../com/mirth/connect/userutil/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Package" class="title">Package&nbsp;com.mirth.connect.server.userutil</h1>
<div class="docSummary">
<div class="block">This package is included in the JavaScript scope on the server.&nbsp;Classes in this package are
 part of the supported User API for use in channels/scripts.&nbsp;Reference to any class in Mirth
 Connect outside of the userutil packages is unsupported.</div>
</div>
<p>See:&nbsp;<a href="#package.description">Description</a></p>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Class Summary table, listing classes, and an explanation">
<caption><span>Class Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Class</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/ACKGenerator.html" title="class in com.mirth.connect.server.userutil">ACKGenerator</a></td>
<td class="colLast">
<div class="block">Allows users to generate HL7 v2.x acknowledgments based on an inbound message, with a specified
 ACK code and custom text message.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/AlertSender.html" title="class in com.mirth.connect.server.userutil">AlertSender</a></td>
<td class="colLast">
<div class="block">Allows users to dispatch error events which can be alerted on.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a></td>
<td class="colLast">
<div class="block">Used to store and retrieve details about message attachments such as the ID, MIME type, and
 content.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/AttachmentUtil.html" title="class in com.mirth.connect.server.userutil">AttachmentUtil</a></td>
<td class="colLast">
<div class="block">Provides utility methods for creating, retrieving, and re-attaching message attachments.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/ChannelMap.html" title="class in com.mirth.connect.server.userutil">ChannelMap</a></td>
<td class="colLast">
<div class="block">A wrapper class for the channel map that checks against the source map in the <a href="../../../../../com/mirth/connect/server/userutil/ChannelMap.html#get-java.lang.Object-"><code>get(key)</code></a> method for legacy support.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/ChannelUtil.html" title="class in com.mirth.connect.server.userutil">ChannelUtil</a></td>
<td class="colLast">
<div class="block">This utility class allows the user to query information from channels or to perform actions on
 channels.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/ContextFactory.html" title="class in com.mirth.connect.server.userutil">ContextFactory</a></td>
<td class="colLast">
<div class="block">Allows the user to retrieve information about the current JavaScript context.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnection.html" title="class in com.mirth.connect.server.userutil">DatabaseConnection</a></td>
<td class="colLast">
<div class="block">Provides the ability to run SQL queries again the database connection object instantiated using
 DatabaseConnectionFactory.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/DatabaseConnectionFactory.html" title="class in com.mirth.connect.server.userutil">DatabaseConnectionFactory</a></td>
<td class="colLast">
<div class="block">Used to create database connection objects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/DateUtil.html" title="class in com.mirth.connect.server.userutil">DateUtil</a></td>
<td class="colLast">
<div class="block">Provides date/time utility methods.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/DestinationSet.html" title="class in com.mirth.connect.server.userutil">DestinationSet</a></td>
<td class="colLast">
<div class="block">Utility class used in the preprocessor or source filter/transformer to prevent the message from
 being sent to specific destinations.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html" title="class in com.mirth.connect.server.userutil">DICOMUtil</a></td>
<td class="colLast">
<div class="block">Provides DICOM utility methods.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/EncryptedData.html" title="class in com.mirth.connect.server.userutil">EncryptedData</a></td>
<td class="colLast">
<div class="block">This object is returned from <a href="../../../../../com/mirth/connect/server/userutil/EncryptionUtil.html#encrypt-byte:A-"><code>EncryptionUtil.encrypt(byte[])</code></a>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/EncryptionUtil.html" title="class in com.mirth.connect.server.userutil">EncryptionUtil</a></td>
<td class="colLast">
<div class="block">This utility class provides some convenience methods for encrypting or decrypting data.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/FileUtil.html" title="class in com.mirth.connect.server.userutil">FileUtil</a></td>
<td class="colLast">
<div class="block">Provides file utility methods.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="class in com.mirth.connect.server.userutil">Future</a>&lt;V&gt;</td>
<td class="colLast">
<div class="block">A <code>Future</code> represents the result of an asynchronous computation.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/HashUtil.html" title="class in com.mirth.connect.server.userutil">HashUtil</a></td>
<td class="colLast">
<div class="block">Provides hash utility methods.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/HTTPUtil.html" title="class in com.mirth.connect.server.userutil">HTTPUtil</a></td>
<td class="colLast">
<div class="block">Provides HTTP utility methods.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/ImmutableResponse.html" title="class in com.mirth.connect.server.userutil">ImmutableResponse</a></td>
<td class="colLast">
<div class="block">This class represents a destination response and is used to retrieve details such as the response
 data, message status, and errors.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/MirthCachedRowSet.html" title="class in com.mirth.connect.server.userutil">MirthCachedRowSet</a></td>
<td class="colLast">
<div class="block">An implementation of CachedRowSet that retrieves values based on the column label value.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/NCPDPUtil.html" title="class in com.mirth.connect.server.userutil">NCPDPUtil</a></td>
<td class="colLast">
<div class="block">Provides NCPDP utility methods.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/RawMessage.html" title="class in com.mirth.connect.server.userutil">RawMessage</a></td>
<td class="colLast">
<div class="block">This class represents a raw message as it is received by a channel, and is used to retrieve
 details such as the raw data or source map.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/ResponseFactory.html" title="class in com.mirth.connect.server.userutil">ResponseFactory</a></td>
<td class="colLast">
<div class="block">Provides methods to create Response objects.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/SerializerFactory.html" title="class in com.mirth.connect.server.userutil">SerializerFactory</a></td>
<td class="colLast">
<div class="block">Used to create a serializer for a specific data type for conversion to and from XML.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnection.html" title="class in com.mirth.connect.server.userutil">SMTPConnection</a></td>
<td class="colLast">
<div class="block">Used to send e-mail messages.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/SMTPConnectionFactory.html" title="class in com.mirth.connect.server.userutil">SMTPConnectionFactory</a></td>
<td class="colLast">
<div class="block">Utility class used to create SMTPConnection object using the server's default SMTP settings.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/SourceMap.html" title="class in com.mirth.connect.server.userutil">SourceMap</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/UUIDGenerator.html" title="class in com.mirth.connect.server.userutil">UUIDGenerator</a></td>
<td class="colLast">
<div class="block">Utility class to create unique identifiers.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/VMRouter.html" title="class in com.mirth.connect.server.userutil">VMRouter</a></td>
<td class="colLast">
<div class="block">Utility class used to dispatch messages to channels.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<table class="typeSummary" border="0" cellpadding="3" cellspacing="0" summary="Enum Summary table, listing enums, and an explanation">
<caption><span>Enum Summary</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Enum</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="../../../../../com/mirth/connect/server/userutil/DeployedState.html" title="enum in com.mirth.connect.server.userutil">DeployedState</a></td>
<td class="colLast">
<div class="block">States of UNDEPLOYED, DEPLOYING, UNDEPLOYING, STARTING, STARTED, PAUSING, PAUSED, STOPPING,
 STOPPED</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
<a name="package.description">
<!--   -->
</a>
<h2 title="Package com.mirth.connect.server.userutil Description">Package com.mirth.connect.server.userutil Description</h2>
<div class="block">This package is included in the JavaScript scope on the server.&nbsp;Classes in this package are
 part of the supported User API for use in channels/scripts.&nbsp;Reference to any class in Mirth
 Connect outside of the userutil packages is unsupported.</div>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li class="navBarCell1Rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/plugins/httpauth/userutil/package-summary.html">Prev&nbsp;Package</a></li>
<li><a href="../../../../../com/mirth/connect/userutil/package-summary.html">Next&nbsp;Package</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/package-summary.html" target="_top">Frames</a></li>
<li><a href="package-summary.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
