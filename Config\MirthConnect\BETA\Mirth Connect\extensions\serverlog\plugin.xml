<pluginMetaData path="serverlog">
	<name>Server Log</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This plugin allows you to view the server log on the Mirth Connect administrator.</description>
	<serverClasses>
		<string>com.mirth.connect.plugins.serverlog.ServerLogProvider</string>
	</serverClasses>
	<clientClasses>
		<string weight="120">com.mirth.connect.plugins.serverlog.ServerLogClient</string>
	</clientClasses>
	<library type="CLIENT" path="serverlog-client.jar" />
	<library type="SHARED" path="serverlog-shared.jar" />
	<library type="SERVER" path="serverlog-server.jar" />
	<apiProvider type="SERVLET_INTERFACE" name="com.mirth.connect.plugins.serverlog.ServerLogServletInterface"/>
	<apiProvider type="SERVER_CLASS" name="com.mirth.connect.plugins.serverlog.ServerLogServlet"/>
</pluginMetaData>
