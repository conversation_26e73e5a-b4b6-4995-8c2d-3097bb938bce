<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:18 PDT 2023 -->
<title>Future</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Future";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/FileUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/HashUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/Future.html" target="_top">Frames</a></li>
<li><a href="Future.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.server.userutil</div>
<h2 title="Class Future" class="title">Class Future&lt;V&gt;</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.server.userutil.Future&lt;V&gt;</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Future.html?is-external=true" title="class or interface in java.util.concurrent">Future</a>&lt;V&gt;</dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">Future&lt;V&gt;</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Future.html?is-external=true" title="class or interface in java.util.concurrent">Future</a>&lt;V&gt;</pre>
<div class="block">A <code>Future</code> represents the result of an asynchronous computation. Methods are provided to
 check if the computation is complete, to wait for its completion, and to retrieve the result of
 the computation. The result can only be retrieved using method <code>get</code> when the computation
 has completed, blocking if necessary until it is ready. Cancellation is performed by the
 <code>cancel</code> method. Additional methods are provided to determine if the task completed
 normally or was cancelled. Once a computation has completed, the computation cannot be cancelled.
 If you would like to use a <code>Future</code> for the sake of cancellability but not provide a usable
 result, you can declare types of the form <code>Future&lt;?&gt;</code> and return <code>null</code> as a result
 of the underlying task.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/Future.html#cancel-boolean-">cancel</a></span>(boolean&nbsp;mayInterruptIfRunning)</code>
<div class="block">Attempts to cancel execution of this task.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="type parameter in Future">V</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/Future.html#get--">get</a></span>()</code>
<div class="block">Waits if necessary for the computation to complete, and then retrieves its result.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="type parameter in Future">V</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-">get</a></span>(long&nbsp;timeoutInMillis)</code>
<div class="block">Waits if necessary for at most the given time for the computation to complete, and then
 retrieves its result, if available.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="type parameter in Future">V</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-java.util.concurrent.TimeUnit-">get</a></span>(long&nbsp;timeout,
   <a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/TimeUnit.html?is-external=true" title="class or interface in java.util.concurrent">TimeUnit</a>&nbsp;unit)</code>
<div class="block">Waits if necessary for at most the given time for the computation to complete, and then
 retrieves its result, if available.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/Future.html#isCancelled--">isCancelled</a></span>()</code>
<div class="block">Returns <code>true</code> if this task was cancelled before it completed normally.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/Future.html#isDone--">isDone</a></span>()</code>
<div class="block">Returns <code>true</code> if this task completed.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="cancel-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cancel</h4>
<pre>public&nbsp;boolean&nbsp;cancel(boolean&nbsp;mayInterruptIfRunning)</pre>
<div class="block">Attempts to cancel execution of this task. This attempt will fail if the task has already
 completed, has already been cancelled, or could not be cancelled for some other reason. If
 successful, and this task has not started when <code>cancel</code> is called, this task should
 never run. If the task has already started, then the <code>mayInterruptIfRunning</code> parameter
 determines whether the thread executing this task should be interrupted in an attempt to stop
 the task.
 
 After this method returns, subsequent calls to <a href="../../../../../com/mirth/connect/server/userutil/Future.html#isDone--"><code>isDone()</code></a> will always return
 <code>true</code>. Subsequent calls to <a href="../../../../../com/mirth/connect/server/userutil/Future.html#isCancelled--"><code>isCancelled()</code></a> will always return <code>true</code> if
 this method returned <code>true</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Future.html?is-external=true#cancel-boolean-" title="class or interface in java.util.concurrent">cancel</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Future.html?is-external=true" title="class or interface in java.util.concurrent">Future</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="type parameter in Future">V</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>mayInterruptIfRunning</code> - <code>true</code> if the thread executing this task should be interrupted; otherwise,
            in-progress tasks are allowed to complete</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>false</code> if the task could not be cancelled, typically because it has already
         completed normally; <code>true</code> otherwise</dd>
</dl>
</li>
</ul>
<a name="isCancelled--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCancelled</h4>
<pre>public&nbsp;boolean&nbsp;isCancelled()</pre>
<div class="block">Returns <code>true</code> if this task was cancelled before it completed normally.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Future.html?is-external=true#isCancelled--" title="class or interface in java.util.concurrent">isCancelled</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Future.html?is-external=true" title="class or interface in java.util.concurrent">Future</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="type parameter in Future">V</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if this task was cancelled before it completed</dd>
</dl>
</li>
</ul>
<a name="isDone--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDone</h4>
<pre>public&nbsp;boolean&nbsp;isDone()</pre>
<div class="block">Returns <code>true</code> if this task completed.
 
 Completion may be due to normal termination, an exception, or cancellation -- in all of these
 cases, this method will return <code>true</code>.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Future.html?is-external=true#isDone--" title="class or interface in java.util.concurrent">isDone</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Future.html?is-external=true" title="class or interface in java.util.concurrent">Future</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="type parameter in Future">V</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>true</code> if this task completed</dd>
</dl>
</li>
</ul>
<a name="get--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="type parameter in Future">V</a>&nbsp;get()
      throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/InterruptedException.html?is-external=true" title="class or interface in java.lang">InterruptedException</a>,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/ExecutionException.html?is-external=true" title="class or interface in java.util.concurrent">ExecutionException</a></pre>
<div class="block">Waits if necessary for the computation to complete, and then retrieves its result.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Future.html?is-external=true#get--" title="class or interface in java.util.concurrent">get</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Future.html?is-external=true" title="class or interface in java.util.concurrent">Future</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="type parameter in Future">V</a>&gt;</code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the computed result</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/CancellationException.html?is-external=true" title="class or interface in java.util.concurrent">CancellationException</a></code> - if the computation was cancelled</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/ExecutionException.html?is-external=true" title="class or interface in java.util.concurrent">ExecutionException</a></code> - if the computation threw an exception</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/InterruptedException.html?is-external=true" title="class or interface in java.lang">InterruptedException</a></code> - if the current thread was interrupted while waiting</dd>
</dl>
</li>
</ul>
<a name="get-long-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="type parameter in Future">V</a>&nbsp;get(long&nbsp;timeoutInMillis)
      throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/InterruptedException.html?is-external=true" title="class or interface in java.lang">InterruptedException</a>,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/ExecutionException.html?is-external=true" title="class or interface in java.util.concurrent">ExecutionException</a>,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/TimeoutException.html?is-external=true" title="class or interface in java.util.concurrent">TimeoutException</a></pre>
<div class="block">Waits if necessary for at most the given time for the computation to complete, and then
 retrieves its result, if available.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>timeoutInMillis</code> - the maximum time to wait, in milliseconds</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the computed result</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/CancellationException.html?is-external=true" title="class or interface in java.util.concurrent">CancellationException</a></code> - if the computation was cancelled</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/ExecutionException.html?is-external=true" title="class or interface in java.util.concurrent">ExecutionException</a></code> - if the computation threw an exception</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/InterruptedException.html?is-external=true" title="class or interface in java.lang">InterruptedException</a></code> - if the current thread was interrupted while waiting</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/TimeoutException.html?is-external=true" title="class or interface in java.util.concurrent">TimeoutException</a></code> - if the wait timed out</dd>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../../../../com/mirth/connect/server/userutil/Future.html#get-long-java.util.concurrent.TimeUnit-"><code>get(long, TimeUnit)</code></a></dd>
</dl>
</li>
</ul>
<a name="get-long-java.util.concurrent.TimeUnit-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>get</h4>
<pre>public&nbsp;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="type parameter in Future">V</a>&nbsp;get(long&nbsp;timeout,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/TimeUnit.html?is-external=true" title="class or interface in java.util.concurrent">TimeUnit</a>&nbsp;unit)
      throws <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/InterruptedException.html?is-external=true" title="class or interface in java.lang">InterruptedException</a>,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/ExecutionException.html?is-external=true" title="class or interface in java.util.concurrent">ExecutionException</a>,
             <a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/TimeoutException.html?is-external=true" title="class or interface in java.util.concurrent">TimeoutException</a></pre>
<div class="block">Waits if necessary for at most the given time for the computation to complete, and then
 retrieves its result, if available.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Future.html?is-external=true#get-long-java.util.concurrent.TimeUnit-" title="class or interface in java.util.concurrent">get</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/Future.html?is-external=true" title="class or interface in java.util.concurrent">Future</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Future.html" title="type parameter in Future">V</a>&gt;</code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>timeout</code> - the maximum time to wait</dd>
<dd><code>unit</code> - the time unit of the timeout argument</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the computed result</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/CancellationException.html?is-external=true" title="class or interface in java.util.concurrent">CancellationException</a></code> - if the computation was cancelled</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/ExecutionException.html?is-external=true" title="class or interface in java.util.concurrent">ExecutionException</a></code> - if the computation threw an exception</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/InterruptedException.html?is-external=true" title="class or interface in java.lang">InterruptedException</a></code> - if the current thread was interrupted while waiting</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/TimeoutException.html?is-external=true" title="class or interface in java.util.concurrent">TimeoutException</a></code> - if the wait timed out</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/FileUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/HashUtil.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/Future.html" target="_top">Frames</a></li>
<li><a href="Future.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
