<pluginMetaData path="datatype-dicom">
	<name>DICOM Data Type</name>
	<author>NextGen Healthcare</author>
	<pluginVersion>4.5.2</pluginVersion>
	<mirthVersion>4.5.2</mirthVersion>
	<url>http://www.nextgen.com</url>
	<description>This plugin provides support for the DICOM data type</description>
	<serverClasses>
		<string>com.mirth.connect.plugins.datatypes.dicom.DICOMDataTypeServerPlugin</string>
	</serverClasses>
	<clientClasses>
		<string>com.mirth.connect.plugins.datatypes.dicom.DICOMDataTypeClientPlugin</string>
	</clientClasses>
	<templateClassName>com.mirth.connect.plugins.datatypes.dicom.DICOMDataTypeCodeTemplatePlugin</templateClassName>
	<library type="CLIENT" path="datatype-dicom-client.jar" />
	<library type="SHARED" path="datatype-dicom-shared.jar" />
	<library type="SERVER" path="datatype-dicom-server.jar" />
</pluginMetaData>
