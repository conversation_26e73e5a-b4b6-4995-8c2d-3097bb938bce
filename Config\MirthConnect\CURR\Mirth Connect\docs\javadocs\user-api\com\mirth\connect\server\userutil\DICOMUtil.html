<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:18 PDT 2023 -->
<title>DICOMUtil</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DICOMUtil";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9,"i7":9,"i8":9,"i9":9,"i10":9,"i11":9,"i12":9,"i13":9,"i14":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/DestinationSet.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/EncryptedData.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/DICOMUtil.html" target="_top">Frames</a></li>
<li><a href="DICOMUtil.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.server.userutil</div>
<h2 title="Class DICOMUtil" class="title">Class DICOMUtil</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.server.userutil.DICOMUtil</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">DICOMUtil</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Provides DICOM utility methods.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static org.dcm4che2.data.DicomObject</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#byteArrayToDicomObject-byte:A-boolean-">byteArrayToDicomObject</a></span>(byte[]&nbsp;bytes,
                      boolean&nbsp;decodeBase64)</code>
<div class="block">Converts a byte array into a dcm4che DicomObject.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOM-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-">convertDICOM</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
            <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</code>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOM-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-boolean-">convertDICOM</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
            <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
            boolean&nbsp;autoThreshold)</code>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOM-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-int-">convertDICOM</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
            <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
            int&nbsp;sliceIndex)</code>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOM-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-int-boolean-">convertDICOM</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
            <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
            int&nbsp;sliceIndex,
            boolean&nbsp;autoThreshold)</code>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOMToByteArray-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-">convertDICOMToByteArray</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
                       <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</code>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOMToByteArray-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-int-">convertDICOMToByteArray</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
                       <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                       int&nbsp;sliceIndex)</code>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#convertDICOMToByteArray-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-int-boolean-">convertDICOMToByteArray</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
                       <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                       int&nbsp;sliceIndex,
                       boolean&nbsp;autoThreshold)</code>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#dicomObjectToByteArray-org.dcm4che2.data.DicomObject-">dicomObjectToByteArray</a></span>(org.dcm4che2.data.DicomObject&nbsp;dicomObject)</code>
<div class="block">Converts a dcm4che DicomObject into a byte array.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#getDICOMMessage-com.mirth.connect.userutil.ImmutableConnectorMessage-">getDICOMMessage</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</code>
<div class="block">Re-attaches DICOM attachments with the header data in the connector message and returns the
 resulting merged data as a byte array.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>static byte[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#getDICOMRawBytes-com.mirth.connect.userutil.ImmutableConnectorMessage-">getDICOMRawBytes</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</code>
<div class="block">Re-attaches DICOM attachments with the header data in the connector message and returns the
 resulting merged data as a byte array.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#getDICOMRawData-com.mirth.connect.userutil.ImmutableConnectorMessage-">getDICOMRawData</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</code>
<div class="block">Re-attaches DICOM attachments with the header data in the connector message and returns the
 resulting merged data as a Base64-encoded string.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#getSliceCount-com.mirth.connect.userutil.ImmutableConnectorMessage-">getSliceCount</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</code>
<div class="block">Returns the number of slices in the fully-merged DICOM data associated with a given connector
 message.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#mergeHeaderAttachments-com.mirth.connect.userutil.ImmutableConnectorMessage-java.util.List-">mergeHeaderAttachments</a></span>(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                      <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;&nbsp;attachments)</code>
<div class="block">Re-attaches DICOM attachments with the header data in the connector message and returns the
 resulting merged data as a Base-64 encoded String.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../../com/mirth/connect/server/userutil/DICOMUtil.html#mergeHeaderPixelData-byte:A-java.util.List-">mergeHeaderPixelData</a></span>(byte[]&nbsp;header,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;byte[]&gt;&nbsp;images)</code>
<div class="block">Re-attaches DICOM attachments with the given header data and returns the resulting merged
 data as a Base-64 encoded String.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDICOMRawData-com.mirth.connect.userutil.ImmutableConnectorMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDICOMRawData</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getDICOMRawData(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</pre>
<div class="block">Re-attaches DICOM attachments with the header data in the connector message and returns the
 resulting merged data as a Base64-encoded string.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message to retrieve merged DICOM data for.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The merged DICOM data, Base64-encoded.</dd>
</dl>
</li>
</ul>
<a name="getDICOMRawBytes-com.mirth.connect.userutil.ImmutableConnectorMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDICOMRawBytes</h4>
<pre>public static&nbsp;byte[]&nbsp;getDICOMRawBytes(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</pre>
<div class="block">Re-attaches DICOM attachments with the header data in the connector message and returns the
 resulting merged data as a byte array.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message to retrieve merged DICOM data for.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The merged DICOM data as a byte array.</dd>
</dl>
</li>
</ul>
<a name="getDICOMMessage-com.mirth.connect.userutil.ImmutableConnectorMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDICOMMessage</h4>
<pre>public static&nbsp;byte[]&nbsp;getDICOMMessage(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</pre>
<div class="block">Re-attaches DICOM attachments with the header data in the connector message and returns the
 resulting merged data as a byte array.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message to retrieve merged DICOM data for.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The merged DICOM data as a byte array.</dd>
</dl>
</li>
</ul>
<a name="mergeHeaderAttachments-com.mirth.connect.userutil.ImmutableConnectorMessage-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mergeHeaderAttachments</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mergeHeaderAttachments(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                            <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../../com/mirth/connect/server/userutil/Attachment.html" title="class in com.mirth.connect.server.userutil">Attachment</a>&gt;&nbsp;attachments)
                                     throws com.mirth.connect.donkey.model.message.MessageSerializerException,
                                            <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Re-attaches DICOM attachments with the header data in the connector message and returns the
 resulting merged data as a Base-64 encoded String.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message containing header data to merge DICOM attachments with.</dd>
<dd><code>attachments</code> - The DICOM attachments to merge with the header data.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The merged DICOM data as a Base-64 encoded String.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>com.mirth.connect.donkey.model.message.MessageSerializerException</code> - If a database access error occurs, or the DICOM data could not be parsed.</dd>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - If Base64 encoding failed.</dd>
</dl>
</li>
</ul>
<a name="mergeHeaderPixelData-byte:A-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mergeHeaderPixelData</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;mergeHeaderPixelData(byte[]&nbsp;header,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;byte[]&gt;&nbsp;images)
                                   throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Re-attaches DICOM attachments with the given header data and returns the resulting merged
 data as a Base-64 encoded String.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>header</code> - The header data to merge DICOM attachments with.</dd>
<dd><code>images</code> - The DICOM attachments as byte arrays to merge with the header data.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The merged DICOM data as a Base-64 encoded String.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - If Base64 encoding failed.</dd>
</dl>
</li>
</ul>
<a name="getSliceCount-com.mirth.connect.userutil.ImmutableConnectorMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSliceCount</h4>
<pre>public static&nbsp;int&nbsp;getSliceCount(<a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</pre>
<div class="block">Returns the number of slices in the fully-merged DICOM data associated with a given connector
 message.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>connectorMessage</code> - The connector message to retrieve DICOM data for.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The number of slices in the DICOM data.</dd>
</dl>
</li>
</ul>
<a name="convertDICOM-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertDICOM</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;convertDICOM(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
                                  <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                  boolean&nbsp;autoThreshold)</pre>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>imageType</code> - The image format to convert the DICOM data to (e.g. "jpg").</dd>
<dd><code>connectorMessage</code> - The connector message to retrieve merged DICOM data for.</dd>
<dd><code>autoThreshold</code> - If true, automatically sets the lower and upper threshold levels.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted image, as a Base64-encoded string.</dd>
</dl>
</li>
</ul>
<a name="convertDICOM-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertDICOM</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;convertDICOM(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
                                  <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</pre>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>imageType</code> - The image format to convert the DICOM data to (e.g. "jpg").</dd>
<dd><code>connectorMessage</code> - The connector message to retrieve merged DICOM data for.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted image, as a Base64-encoded string.</dd>
</dl>
</li>
</ul>
<a name="convertDICOM-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertDICOM</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;convertDICOM(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
                                  <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                  int&nbsp;sliceIndex)</pre>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>imageType</code> - The image format to convert the DICOM data to (e.g. "jpg").</dd>
<dd><code>connectorMessage</code> - The connector message to retrieve merged DICOM data for.</dd>
<dd><code>sliceIndex</code> - If there are multiple slices in the DICOM data, this indicates which one to use
            (the first slice has an index of 1).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted image, as a Base64-encoded string.</dd>
</dl>
</li>
</ul>
<a name="convertDICOM-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertDICOM</h4>
<pre>public static&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;convertDICOM(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
                                  <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                  int&nbsp;sliceIndex,
                                  boolean&nbsp;autoThreshold)</pre>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>imageType</code> - The image format to convert the DICOM data to (e.g. "jpg").</dd>
<dd><code>connectorMessage</code> - The connector message to retrieve merged DICOM data for.</dd>
<dd><code>sliceIndex</code> - If there are multiple slices in the DICOM data, this indicates which one to use
            (the first slice has an index of 1).</dd>
<dd><code>autoThreshold</code> - If true, automatically sets the lower and upper threshold levels.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted image, as a Base64-encoded string.</dd>
</dl>
</li>
</ul>
<a name="convertDICOMToByteArray-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertDICOMToByteArray</h4>
<pre>public static&nbsp;byte[]&nbsp;convertDICOMToByteArray(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
                                             <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage)</pre>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>imageType</code> - The image format to convert the DICOM data to (e.g. "jpg").</dd>
<dd><code>connectorMessage</code> - The connector message to retrieve merged DICOM data for.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted image, as a byte array.</dd>
</dl>
</li>
</ul>
<a name="convertDICOMToByteArray-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertDICOMToByteArray</h4>
<pre>public static&nbsp;byte[]&nbsp;convertDICOMToByteArray(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
                                             <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                             int&nbsp;sliceIndex)</pre>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>imageType</code> - The image format to convert the DICOM data to (e.g. "jpg").</dd>
<dd><code>connectorMessage</code> - The connector message to retrieve merged DICOM data for.</dd>
<dd><code>sliceIndex</code> - If there are multiple slices in the DICOM data, this indicates which one to use
            (the first slice has an index of 1).</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted image, as a byte array.</dd>
</dl>
</li>
</ul>
<a name="convertDICOMToByteArray-java.lang.String-com.mirth.connect.userutil.ImmutableConnectorMessage-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertDICOMToByteArray</h4>
<pre>public static&nbsp;byte[]&nbsp;convertDICOMToByteArray(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;imageType,
                                             <a href="../../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;connectorMessage,
                                             int&nbsp;sliceIndex,
                                             boolean&nbsp;autoThreshold)</pre>
<div class="block">Converts merged DICOM data associated with a connector message into a specified image format.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>imageType</code> - The image format to convert the DICOM data to (e.g. "jpg").</dd>
<dd><code>connectorMessage</code> - The connector message to retrieve merged DICOM data for.</dd>
<dd><code>sliceIndex</code> - If there are multiple slices in the DICOM data, this indicates which one to use
            (the first slice has an index of 1).</dd>
<dd><code>autoThreshold</code> - If true, automatically sets the lower and upper threshold levels.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted image, as a byte array.</dd>
</dl>
</li>
</ul>
<a name="byteArrayToDicomObject-byte:A-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>byteArrayToDicomObject</h4>
<pre>public static&nbsp;org.dcm4che2.data.DicomObject&nbsp;byteArrayToDicomObject(byte[]&nbsp;bytes,
                                                                   boolean&nbsp;decodeBase64)
                                                            throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Converts a byte array into a dcm4che DicomObject.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>bytes</code> - The binary data to convert.</dd>
<dd><code>decodeBase64</code> - If true, the data is assumed to be Base64-encoded.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted DicomObject.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - If Base64 encoding failed.</dd>
</dl>
</li>
</ul>
<a name="dicomObjectToByteArray-org.dcm4che2.data.DicomObject-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>dicomObjectToByteArray</h4>
<pre>public static&nbsp;byte[]&nbsp;dicomObjectToByteArray(org.dcm4che2.data.DicomObject&nbsp;dicomObject)
                                     throws <a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></pre>
<div class="block">Converts a dcm4che DicomObject into a byte array.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>dicomObject</code> - The DicomObject to convert.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The converted byte array.</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/IOException.html?is-external=true" title="class or interface in java.io">IOException</a></code> - If Base64 encoding failed.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../../com/mirth/connect/server/userutil/DestinationSet.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../../com/mirth/connect/server/userutil/EncryptedData.html" title="class in com.mirth.connect.server.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../../index.html?com/mirth/connect/server/userutil/DICOMUtil.html" target="_top">Frames</a></li>
<li><a href="DICOMUtil.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
