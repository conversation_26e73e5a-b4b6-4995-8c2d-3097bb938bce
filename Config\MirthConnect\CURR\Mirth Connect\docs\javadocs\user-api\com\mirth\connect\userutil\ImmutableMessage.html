<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc (1.8.0_111) on Mon Oct 23 12:19:21 PDT 2023 -->
<title>ImmutableMessage</title>
<meta name="date" content="2023-10-23">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ImmutableMessage";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":42,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":42,"i11":10,"i12":10,"i13":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"],32:["t6","Deprecated Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/mirth/connect/userutil/ImmutableMessage.html" target="_top">Frames</a></li>
<li><a href="ImmutableMessage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">com.mirth.connect.userutil</div>
<h2 title="Class ImmutableMessage" class="title">Class ImmutableMessage</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>com.mirth.connect.userutil.ImmutableMessage</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ImmutableMessage</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">This class represents an overall message and is used to retrieve details such as the message ID,
 specific connector messages, or the merged connector message.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#ImmutableMessage-com.mirth.connect.donkey.model.message.Message-">ImmutableMessage</a></span>(com.mirth.connect.donkey.model.message.Message&nbsp;message)</code>
<div class="block">Instantiates a new ImmutableMessage object.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t6" class="tableTab"><span><a href="javascript:show(32);">Deprecated Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../com/mirth/connect/userutil/ImmutableAttachment.html" title="class in com.mirth.connect.userutil">ImmutableAttachment</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getAttachments--">getAttachments</a></span>()</code>
<div class="block">Returns a list of attachments associated with this message.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getChannelId--">getChannelId</a></span>()</code>
<div class="block">Returns the ID of the channel associated with this message.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getConnectorMessages--">getConnectorMessages</a></span>()</code>
<div class="block">Returns a map of connector messages associated with this message.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getDestinationIdMap--">getDestinationIdMap</a></span>()</code>
<div class="block">Returns a Map of destination connector names linked to their corresponding connector metadata
 ID.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getDestinationNameMap--">getDestinationNameMap</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getDestinationIdMap--"><code>getDestinationIdMap()</code></a> instead.</span></div>
</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getImportChannelId--">getImportChannelId</a></span>()</code>
<div class="block">Returns the ID of the original channel this message was reprocessed from.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getImportId--">getImportId</a></span>()</code>
<div class="block">Returns the ID of the original message this one was imported from.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getMergedConnectorMessage--">getMergedConnectorMessage</a></span>()</code>
<div class="block">Returns a "merged" connector message containing data from all connector messages combined.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getMessageId--">getMessageId</a></span>()</code>
<div class="block">Returns the sequential ID of this message, as a Long.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getOriginalId--">getOriginalId</a></span>()</code>
<div class="block">Returns the ID of the original message this one was reprocessed from.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Calendar.html?is-external=true" title="class or interface in java.util">Calendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getReceivedDate--">getReceivedDate</a></span>()</code>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;
<div class="block"><span class="deprecationComment">This method is deprecated and will soon be removed. This method currently returns
             the received date of the source connector message.</span></div>
</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getServerId--">getServerId</a></span>()</code>
<div class="block">Returns the ID of the server associated with this message.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#isProcessed--">isProcessed</a></span>()</code>
<div class="block">Returns whether this message has finished processing through a channel.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#toString--">toString</a></span>()</code>
<div class="block">Returns a string representation of the object.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ImmutableMessage-com.mirth.connect.donkey.model.message.Message-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ImmutableMessage</h4>
<pre>public&nbsp;ImmutableMessage(com.mirth.connect.donkey.model.message.Message&nbsp;message)</pre>
<div class="block">Instantiates a new ImmutableMessage object.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>message</code> - The Message object that this object will reference for retrieving data.</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getMessageId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMessageId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getMessageId()</pre>
<div class="block">Returns the sequential ID of this message, as a Long.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The sequential ID of this message, as a Long.</dd>
</dl>
</li>
</ul>
<a name="getServerId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getServerId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getServerId()</pre>
<div class="block">Returns the ID of the server associated with this message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The ID of the server associated with this message.</dd>
</dl>
</li>
</ul>
<a name="getChannelId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getChannelId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getChannelId()</pre>
<div class="block">Returns the ID of the channel associated with this message.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The ID of the channel associated with this message.</dd>
</dl>
</li>
</ul>
<a name="getReceivedDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getReceivedDate</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Calendar.html?is-external=true" title="class or interface in java.util">Calendar</a>&nbsp;getReceivedDate()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. This method currently returns
             the received date of the source connector message.</span></div>
<div class="block">Returns the original date/time that this message was created by the channel. If the message
 is reprocessed at a later point, this date will remain the same and instead the connector
 message received dates will be updated.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The original date/time that this message was created by the channel.</dd>
</dl>
</li>
</ul>
<a name="isProcessed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isProcessed</h4>
<pre>public&nbsp;boolean&nbsp;isProcessed()</pre>
<div class="block">Returns whether this message has finished processing through a channel. A message is
 considered "processed" if it correctly flows through each applicable connector and the
 postprocessor script finishes. Even if a non-fatal error occurs on a particular connector
 message and the status ends up as ERROR, or if a message is queued by a destination and has
 not yet been sent to the outbound system, it can still be considered processed.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A boolean indicating whether this message has finished processing through a channel.</dd>
</dl>
</li>
</ul>
<a name="getOriginalId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOriginalId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getOriginalId()</pre>
<div class="block">Returns the ID of the original message this one was reprocessed from.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The ID of the original message this one was reprocessed from.</dd>
</dl>
</li>
</ul>
<a name="getImportId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImportId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Long.html?is-external=true" title="class or interface in java.lang">Long</a>&nbsp;getImportId()</pre>
<div class="block">Returns the ID of the original message this one was imported from.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The ID of the original message this one was imported from.</dd>
</dl>
</li>
</ul>
<a name="getImportChannelId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getImportChannelId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getImportChannelId()</pre>
<div class="block">Returns the ID of the original channel this message was reprocessed from.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>The ID of the original channel this message was reprocessed from.</dd>
</dl>
</li>
</ul>
<a name="getAttachments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAttachments</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../com/mirth/connect/userutil/ImmutableAttachment.html" title="class in com.mirth.connect.userutil">ImmutableAttachment</a>&gt;&nbsp;getAttachments()</pre>
<div class="block">Returns a list of attachments associated with this message. This will only be populated in
 certain cases, such as when a message is being exported or archived.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A list of attachments associated with this message.</dd>
</dl>
</li>
</ul>
<a name="getConnectorMessages--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getConnectorMessages</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&gt;&nbsp;getConnectorMessages()</pre>
<div class="block">Returns a map of connector messages associated with this message. The keys are the metadata
 IDs (as Integer objects), and the values are the connector messages themselves.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A map of connector messages associated with this message.</dd>
</dl>
</li>
</ul>
<a name="getMergedConnectorMessage--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMergedConnectorMessage</h4>
<pre>public&nbsp;<a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil">ImmutableConnectorMessage</a>&nbsp;getMergedConnectorMessage()</pre>
<div class="block">Returns a "merged" connector message containing data from all connector messages combined.
 The raw and processed raw content is copied from the source connector, while values in the
 channel and response maps are copied from all connectors.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A "merged" connector message containing data from all connector messages combined.</dd>
</dl>
</li>
</ul>
<a name="getDestinationNameMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinationNameMap</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Deprecated.html?is-external=true" title="class or interface in java.lang">@Deprecated</a>
public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;getDestinationNameMap()</pre>
<div class="block"><span class="deprecatedLabel">Deprecated.</span>&nbsp;<span class="deprecationComment">This method is deprecated and will soon be removed. Please use
             <a href="../../../../com/mirth/connect/userutil/ImmutableMessage.html#getDestinationIdMap--"><code>getDestinationIdMap()</code></a> instead.</span></div>
<div class="block">Returns a Map of destination connector names linked to their corresponding "d#" response map
 keys (where "#" is the destination connector metadata ID).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A Map of destination connector names linked to their corresponding "d#" response map
         keys.</dd>
</dl>
</li>
</ul>
<a name="getDestinationIdMap--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDestinationIdMap</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;getDestinationIdMap()</pre>
<div class="block">Returns a Map of destination connector names linked to their corresponding connector metadata
 ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>A Map of destination connector names linked to their corresponding connector metadata
         ID.</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<div class="block">Returns a string representation of the object. In general, the
 <code>toString</code> method returns a string that
 "textually represents" this object. The result should
 be a concise but informative representation that is easy for a
 person to read.
 It is recommended that all subclasses override this method.
 <p>
 The <code>toString</code> method for class <code>Object</code>
 returns a string consisting of the name of the class of which the
 object is an instance, the at-sign character `<code>@</code>', and
 the unsigned hexadecimal representation of the hash code of the
 object. In other words, this method returns a string equal to the
 value of:
 <blockquote>
 <pre>
 getClass().getName() + '@' + Integer.toHexString(hashCode())
 </pre></blockquote></div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>a string representation of the object.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../com/mirth/connect/userutil/ImmutableConnectorMessage.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../com/mirth/connect/userutil/ImmutableMessageContent.html" title="class in com.mirth.connect.userutil"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?com/mirth/connect/userutil/ImmutableMessage.html" target="_top">Frames</a></li>
<li><a href="ImmutableMessage.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
</body>
</html>
